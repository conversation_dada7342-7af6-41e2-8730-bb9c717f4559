# FileHelper Documentation

## Overview
The `FileHelper` class provides comprehensive file operations, size formatting, and storage management utilities.

## Purpose
- Format file sizes in human-readable formats
- Handle file uploads and naming
- Manage file deletion across storage disks
- Validate file extensions and properties

## Key Features
- ✅ **Multiple Units** - Support for B, KB, MB, GB, TB
- ✅ **Storage Integration** - Works with Laravel's Storage facade
- ✅ **Unique Naming** - Generate collision-free filenames
- ✅ **Validation** - File extension and existence checking
- ✅ **Error Handling** - Graceful handling of missing files

## Constants
```php
private const SIZE_UNITS = [
    'b' => 1,
    'kb' => 1024,
    'mb' => 1048576,
    'gb' => 1073741824,
    'tb' => 1099511627776,
];
```

## Methods

### Size Formatting

#### `formatSize($fileSize, string $unit = 'MB', int $decimals = 2): string`
Formats file size in human-readable format.

**Parameters:**
- `$fileSize` (int|float|string|null) - File size in bytes
- `$unit` (string) - Target unit (B, KB, MB, GB, TB)
- `$decimals` (int) - Number of decimal places

**Returns:**
- `string` - Formatted file size

**Usage:**
```php
// Basic formatting
$size = FileHelper::formatSize(1048576); // 1MB in bytes
// Output: "1.00 MB"

// Different units
$size = FileHelper::formatSize(1024, 'KB');
// Output: "1.00 KB"

// Custom decimals
$size = FileHelper::formatSize(1536, 'KB', 0);
// Output: "2 KB"

// Handle null/empty
$size = FileHelper::formatSize(null);
// Output: "0 MB"
```

### Size Conversion

#### `toBytes($fileSize, string $from = 'mb'): float`
Converts file size to bytes from various units.

**Usage:**
```php
// From MB to bytes
$bytes = FileHelper::toBytes(5, 'mb');
// Output: 5242880.0

// From string with unit
$bytes = FileHelper::toBytes('10MB', 'mb');
// Output: 10485760.0

// From KB to bytes
$bytes = FileHelper::toBytes(1024, 'kb');
// Output: 1048576.0
```

#### `toKb($fileSize, string $from = 'mb'): float`
Converts file size to KB from various units.

**Usage:**
```php
// From MB to KB
$kb = FileHelper::toKb(1, 'mb');
// Output: 1024.0

// From bytes to KB
$kb = FileHelper::toKb(2048, 'b');
// Output: 2.0
```

### File Naming

#### `generateHashFileName(?string $extension = null): string`
Generates a unique hash-based filename.

**Usage:**
```php
// Without extension
$filename = FileHelper::generateHashFileName();
// Output: "a1b2c3d4e5f6_abc123_1642598400"

// With extension
$filename = FileHelper::generateHashFileName('jpg');
// Output: "a1b2c3d4e5f6_abc123_1642598400.jpg"
```

#### `generateHashFileNameFromUpload(UploadedFile $file): string`
Generates filename from uploaded file with original extension.

**Usage:**
```php
// In controller
public function upload(Request $request)
{
    $file = $request->file('upload');
    $filename = FileHelper::generateHashFileNameFromUpload($file);
    
    $file->storeAs('uploads', $filename, 'public');
    
    return response()->json(['filename' => $filename]);
}
```

### File Operations

#### `deleteFile(?string $filename, string $directory = '', string $disk = 'public'): bool`
Deletes a file from storage disk.

**Usage:**
```php
// Delete from public disk
$deleted = FileHelper::deleteFile('image.jpg', 'uploads');

// Delete from specific disk
$deleted = FileHelper::deleteFile('document.pdf', 'documents', 's3');

// Returns true even if file doesn't exist
$result = FileHelper::deleteFile('nonexistent.txt');
// Output: true
```

#### `unlinkOldFile(?string $fieldName, string $publicFileDir): void`
Legacy method for deleting files from public storage.

**Usage:**
```php
// Backward compatibility
FileHelper::unlinkOldFile($oldFilename, 'uploads');
```

### File Information

#### `getFileSize(string $filePath, string $disk = 'public', string $unit = 'MB'): string`
Gets file size in human-readable format from storage.

**Usage:**
```php
$size = FileHelper::getFileSize('uploads/image.jpg');
// Output: "2.45 MB"

$sizeKB = FileHelper::getFileSize('uploads/image.jpg', 'public', 'KB');
// Output: "2510.34 KB"
```

#### `fileExists(string $filePath, string $disk = 'public'): bool`
Checks if file exists in storage.

**Usage:**
```php
if (FileHelper::fileExists('uploads/image.jpg')) {
    // File exists, safe to process
    $size = FileHelper::getFileSize('uploads/image.jpg');
}
```

#### `getMimeType(string $filePath, string $disk = 'public'): ?string`
Gets file MIME type.

**Usage:**
```php
$mimeType = FileHelper::getMimeType('uploads/image.jpg');
// Output: "image/jpeg" or null if file doesn't exist

if (str_starts_with($mimeType, 'image/')) {
    // It's an image file
    $thumbnail = generateThumbnail($filePath);
}
```

#### `validateExtension(string $filename, array $allowedExtensions): bool`
Validates file extension against allowed extensions.

**Usage:**
```php
$allowed = ['jpg', 'png', 'gif', 'webp'];
$isValid = FileHelper::validateExtension('image.jpg', $allowed);
// Output: true

$isInvalid = FileHelper::validateExtension('script.php', $allowed);
// Output: false

// Case insensitive
$isValid = FileHelper::validateExtension('IMAGE.JPG', ['jpg', 'png']);
// Output: true
```

## Use Cases

### 1. File Upload Processing
```php
public function uploadFile(Request $request)
{
    $file = $request->file('upload');
    
    // Validate extension
    $allowedTypes = ['jpg', 'png', 'pdf', 'docx'];
    if (!FileHelper::validateExtension($file->getClientOriginalName(), $allowedTypes)) {
        return response()->json(['error' => 'Invalid file type'], 422);
    }
    
    // Generate unique filename
    $filename = FileHelper::generateHashFileNameFromUpload($file);
    
    // Store file
    $path = $file->storeAs('uploads', $filename, 'public');
    
    // Get file size for database
    $size = FileHelper::getFileSize("uploads/{$filename}");
    
    return response()->json([
        'filename' => $filename,
        'size' => $size,
        'path' => $path
    ]);
}
```

### 2. File Management Interface
```php
public function getFileInfo($filename)
{
    $path = "uploads/{$filename}";
    
    if (!FileHelper::fileExists($path)) {
        return ['error' => 'File not found'];
    }
    
    return [
        'exists' => true,
        'size' => FileHelper::getFileSize($path),
        'mime_type' => FileHelper::getMimeType($path),
        'size_bytes' => Storage::disk('public')->size($path)
    ];
}
```

### 3. Cleanup Operations
```php
public function cleanupOldFiles()
{
    $oldFiles = DB::table('uploads')
        ->where('created_at', '<', now()->subDays(30))
        ->pluck('filename');
    
    $deletedCount = 0;
    foreach ($oldFiles as $filename) {
        if (FileHelper::deleteFile($filename, 'uploads')) {
            $deletedCount++;
        }
    }
    
    return "Deleted {$deletedCount} files";
}
```

### 4. File Size Display
```blade
<!-- In Blade templates -->
<div class="file-list">
    @foreach($files as $file)
        <div class="file-item">
            <span class="filename">{{ $file->name }}</span>
            <span class="filesize">{{ FileHelper::formatSize($file->size_bytes, 'MB', 1) }}</span>
        </div>
    @endforeach
</div>
```

### 5. Storage Quota Management
```php
public function checkStorageQuota($userId)
{
    $userFiles = UserFile::where('user_id', $userId)->get();
    $totalBytes = 0;
    
    foreach ($userFiles as $file) {
        if (FileHelper::fileExists($file->path)) {
            $totalBytes += Storage::disk('public')->size($file->path);
        }
    }
    
    $totalSize = FileHelper::formatSize($totalBytes, 'GB', 2);
    $quotaLimit = '5.00 GB';
    
    return [
        'used' => $totalSize,
        'limit' => $quotaLimit,
        'percentage' => ($totalBytes / FileHelper::toBytes(5, 'gb')) * 100
    ];
}
```

## Best Practices

1. **Always Validate Extensions**
   ```php
   // Good
   if (FileHelper::validateExtension($filename, $allowedTypes)) {
       // Process file
   }
   
   // Avoid trusting user input
   $extension = pathinfo($filename, PATHINFO_EXTENSION); // Can be spoofed
   ```

2. **Use Unique Filenames**
   ```php
   // Good - prevents collisions
   $filename = FileHelper::generateHashFileNameFromUpload($file);
   
   // Avoid - can cause conflicts
   $filename = $file->getClientOriginalName();
   ```

3. **Check File Existence**
   ```php
   // Good
   if (FileHelper::fileExists($path)) {
       $size = FileHelper::getFileSize($path);
   }
   
   // Avoid - may throw exceptions
   $size = Storage::disk('public')->size($path);
   ```

4. **Handle Different Storage Disks**
   ```php
   // Flexible approach
   $disk = config('filesystems.default');
   FileHelper::deleteFile($filename, 'uploads', $disk);
   ```

## Migration from Legacy Code

### Before (Legacy)
```php
// Old way
$size = FileHelper::formatSize($fileSize); // Limited options
FileHelper::unlinkOldFile($filename, $dir); // Only public disk
```

### After (Enhanced)
```php
// New way - more flexible
$size = FileHelper::formatSize($fileSize, 'KB', 1);
$deleted = FileHelper::deleteFile($filename, $dir, 'public');

// New capabilities
$unique = FileHelper::generateHashFileName('jpg');
$valid = FileHelper::validateExtension($filename, ['jpg', 'png']);
$exists = FileHelper::fileExists($path);
```

## Related Classes
- [AssetHelper](AssetHelper.md) - For asset versioning
- [DzHelper](DzHelper.md) - For Dropzone file uploads
- [ValidationHelper](ValidationHelper.md) - For file validation rules
