# DateHelper Documentation

## Overview
The `DateHelper` class provides comprehensive date and time formatting utilities with support for JSON arrays and configurable formats.

## Purpose
- Format dates, times, and datetimes consistently across the application
- Handle JSON arrays of date values
- Support both display and value formats
- Provide safe date parsing with Carbon integration

## Key Features
- ✅ **Type Safety** - Full type hints and null safety
- ✅ **JSON Support** - Handles JSON arrays of dates
- ✅ **Configuration-Driven** - Uses application config for formats
- ✅ **Error Handling** - Graceful handling of invalid dates
- ✅ **Carbon Integration** - Safe Carbon instance creation

## Methods

### Core Formatting Methods

#### `formatDate(?string $date, bool $isValueFormat = false): ?string`
Formats a date string according to configuration.

**Parameters:**
- `$date` (string|null) - Date to format
- `$isValueFormat` (bool) - Whether to use value format or display format

**Returns:**
- `string|null` - Formatted date or null if invalid

**Usage:**
```php
// Display format (default: 'd/m/Y')
$display = DateHelper::formatDate('2024-01-15');
// Output: "15/01/2024"

// Value format (default: 'Y-m-d') 
$value = DateHelper::formatDate('15/01/2024', true);
// Output: "2024-01-15"

// Handle null values
$result = DateHelper::formatDate(null);
// Output: null
```

#### `formatDateTime(?string $datetime, bool $isValueFormat = false): ?string`
Formats datetime strings with time components.

**Usage:**
```php
// Display format (default: 'd/m/Y h:i A')
$display = DateHelper::formatDateTime('2024-01-15 14:30:00');
// Output: "15/01/2024 02:30 PM"

// Value format (default: 'Y-m-d H:i:s')
$value = DateHelper::formatDateTime('15/01/2024 2:30 PM', true);
// Output: "2024-01-15 14:30:00"

// JSON array support
$jsonDates = '["2024-01-15 14:30:00", "2024-01-16 09:00:00"]';
$formatted = DateHelper::formatDateTime($jsonDates);
// Output: JSON array with formatted dates
```

#### `formatTime(?string $time, bool $isValueFormat = false): ?string`
Formats time strings.

**Usage:**
```php
// Display format (default: 'h:i A')
$display = DateHelper::formatTime('14:30:00');
// Output: "02:30 PM"

// Value format (default: 'H:i')
$value = DateHelper::formatTime('2:30 PM', true);
// Output: "14:30"
```

### Utility Methods

#### `getCurrentDate(?string $format = null): string`
Gets current date in specified format.

**Usage:**
```php
// Using default config format
$current = DateHelper::getCurrentDate();
// Output: "23/01/2024" (based on config)

// Custom format
$custom = DateHelper::getCurrentDate('Y-m-d');
// Output: "2024-01-23"
```

#### `getCurrentDateTime(?string $format = null): string`
Gets current datetime in specified format.

**Usage:**
```php
$current = DateHelper::getCurrentDateTime();
// Output: "23/01/2024 02:30 PM"

$custom = DateHelper::getCurrentDateTime('Y-m-d H:i:s');
// Output: "2024-01-23 14:30:00"
```

#### `parseDate(?string $date): ?Carbon`
Safely parses date string to Carbon instance.

**Usage:**
```php
$carbon = DateHelper::parseDate('2024-01-15');
if ($carbon) {
    $dayName = $carbon->format('l'); // "Monday"
    $timestamp = $carbon->timestamp;
}

// Invalid date returns null
$invalid = DateHelper::parseDate('invalid-date');
// Output: null
```

#### `isJson($string): bool`
Validates if string is valid JSON.

**Usage:**
```php
$isValid = DateHelper::isJson('["2024-01-15", "2024-01-16"]');
// Output: true

$isInvalid = DateHelper::isJson('not-json');
// Output: false
```

## Configuration

The helper uses configuration from `config/crm_core.php`:

```php
// config/crm_core.php
'format' => [
    'date' => 'd/m/Y',           // Display format
    'time' => 'h:i A',           // Display format
    'datetime' => 'd/m/Y h:i A', // Display format
    
    'value' => [
        'date' => 'Y-m-d',       // Value format
        'time' => 'H:i',         // Value format  
        'datetime' => 'Y-m-d H:i:s', // Value format
    ]
],
```

## Use Cases

### 1. Datatable Column Formatting
```php
// In datatable columns
public function getCreatedAtAttribute($value)
{
    return DateHelper::formatDateTime($value);
}
```

### 2. Form Input Values
```php
// Controller
public function edit(User $user)
{
    $user->birth_date = DateHelper::formatDate($user->birth_date, true);
    return view('users.edit', compact('user'));
}
```

### 3. API Responses
```php
// API Resource
public function toArray($request)
{
    return [
        'id' => $this->id,
        'name' => $this->name,
        'created_at' => DateHelper::formatDateTime($this->created_at, true),
        'updated_at' => DateHelper::formatDateTime($this->updated_at, true),
    ];
}
```

### 4. JSON Array Processing
```php
// Handle multiple dates from forms
public function store(Request $request)
{
    $appointments = $request->appointment_dates; // JSON string
    $formatted = DateHelper::formatDateTime($appointments, true);
    
    // Process formatted dates...
}
```

### 5. Safe Date Operations
```php
// Calculate age safely
public function calculateAge($birthDate)
{
    $birth = DateHelper::parseDate($birthDate);
    
    if (!$birth) {
        return null; // Invalid date
    }
    
    return $birth->age;
}
```

## Advanced Features

### JSON Array Support
The helper automatically detects and processes JSON arrays:

```php
$jsonDates = '["2024-01-15", "2024-01-16", null, "2024-01-17"]';
$formatted = DateHelper::formatDate($jsonDates);
// Processes each date in the array, handles null values
```

### AM/PM Format Detection
Prevents re-formatting already formatted AM/PM times:

```php
$alreadyFormatted = "02:30 PM";
$result = DateHelper::formatTime($alreadyFormatted);
// Returns original string, doesn't double-format
```

## Migration from Legacy Code

### Before (FrontHelper - Deprecated)
```php
// Old way - deprecated
$date = FrontHelper::showDate($date);
$datetime = FrontHelper::showDatetime($datetime);
$time = FrontHelper::showTime($time);
$isJson = FrontHelper::isJson($string);
```

### After (DateHelper - Recommended)
```php
// New way - recommended
$date = DateHelper::formatDate($date);
$datetime = DateHelper::formatDateTime($datetime);
$time = DateHelper::formatTime($time);
$isJson = HtmlHelper::isJson($string); // Moved to HtmlHelper
```

## Best Practices

1. **Always Use Type-Safe Methods**
   ```php
   // Good - handles null safely
   $formatted = DateHelper::formatDate($date);
   
   // Avoid direct Carbon usage without validation
   $formatted = Carbon::parse($date)->format('d/m/Y'); // Can throw exception
   ```

2. **Use Appropriate Formats**
   ```php
   // For display
   $display = DateHelper::formatDate($date, false);
   
   // For database/API storage
   $value = DateHelper::formatDate($date, true);
   ```

3. **Handle Edge Cases**
   ```php
   $carbon = DateHelper::parseDate($userInput);
   if ($carbon) {
       // Safe to use Carbon methods
       $formatted = $carbon->format('Y-m-d');
   } else {
       // Handle invalid date
       $formatted = 'Invalid date';
   }
   ```

## Error Handling
All methods gracefully handle errors and invalid inputs:
- Invalid dates return `null` or fallback values
- JSON parsing errors are caught and logged
- Configuration fallbacks ensure methods always return valid formats

## Related Classes
- [HtmlHelper](HtmlHelper.md) - For JSON validation utilities
- [DatatableHelper](DatatableHelper.md) - Uses DateHelper for datatable formatting
- [FrontHelper](FrontHelper.md) - Contains deprecated date methods
