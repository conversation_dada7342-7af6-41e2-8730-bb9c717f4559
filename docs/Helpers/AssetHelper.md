# AssetHelper Documentation

## Overview
The `AssetHelper` class provides utilities for asset management, versioning, and URL handling in the CRM application.

## Purpose
- Generate versioned asset URLs for cache-busting
- Handle file version tracking
- Manage URL parameters for different views (trash, active records)

## Methods

### `versionedAsset(string $path): string`
Generates an asset URL with a version parameter for cache-busting.

**Parameters:**
- `$path` (string) - Asset path relative to public directory

**Returns:**
- `string` - Asset URL with version parameter

**Usage:**
```php
// Basic usage
$cssUrl = AssetHelper::versionedAsset('/css/app.css');
// Output: /css/app.css?v=1642598400

// JavaScript files
$jsUrl = AssetHelper::versionedAsset('/js/app.js');
// Output: /js/app.js?v=1642598401
```

**Why Use This:**
- Ensures browsers reload assets when files change
- Improves cache management
- Better performance for production environments

### `getFileVersion(string $filePath): string`
Gets version string based on file's last modified time.

**Parameters:**
- `$filePath` (string) - Full path to file

**Returns:**
- `string` - Version string (timestamp or fallback)

**Usage:**
```php
$version = AssetHelper::getFileVersion(public_path('css/app.css'));
// Output: "1642598400" or "1.0.0" if file doesn't exist
```

### `getTrashUrlParameter(): string`
Checks if current URL contains trash parameter and returns appropriate suffix.

**Returns:**
- `string` - '/trash' if in trash view, empty string otherwise

**Usage:**
```php
$trashParam = AssetHelper::getTrashUrlParameter();
if ($trashParam) {
    // User is viewing deleted records
    $pageTitle = 'Deleted Records';
} else {
    // User is viewing active records
    $pageTitle = 'Active Records';
}
```

## Use Cases

### 1. Layout Files
```blade
<!-- In Blade templates -->
<link href="{{ AssetHelper::versionedAsset('/css/app.css') }}" rel="stylesheet">
<script src="{{ AssetHelper::versionedAsset('/js/app.js') }}"></script>
```

### 2. Dynamic Asset Loading
```php
// Controller
public function dashboard()
{
    $assets = [
        'css' => AssetHelper::versionedAsset('/css/dashboard.css'),
        'js' => AssetHelper::versionedAsset('/js/dashboard.js'),
    ];
    
    return view('dashboard', compact('assets'));
}
```

### 3. Conditional Content Based on View Type
```php
// In controllers or middleware
public function index()
{
    $isTrashView = AssetHelper::getTrashUrlParameter() !== '';
    
    if ($isTrashView) {
        return $this->trashedRecords();
    }
    
    return $this->activeRecords();
}
```

## Best Practices

1. **Always Use Versioned Assets in Production**
   ```php
   // Good
   AssetHelper::versionedAsset('/css/app.css');
   
   // Avoid in production
   asset('/css/app.css');
   ```

2. **Cache Version Checks**
   ```php
   // Consider caching for frequently accessed files
   $version = Cache::remember('app_css_version', 3600, function() {
       return AssetHelper::getFileVersion(public_path('css/app.css'));
   });
   ```

3. **Environment-Specific Usage**
   ```php
   // Only version in production
   $assetUrl = app()->environment('production') 
       ? AssetHelper::versionedAsset($path)
       : asset($path);
   ```

## Migration from Legacy Code

### Before (FrontHelper)
```php
// Old way - deprecated
$url = FrontHelper::assetVersion('/css/app.css');
$version = FrontHelper::version(public_path('file.css'));
$trashParam = FrontHelper::getUrlTrashParams();
```

### After (AssetHelper)
```php
// New way - recommended
$url = AssetHelper::versionedAsset('/css/app.css');
$version = AssetHelper::getFileVersion(public_path('file.css'));
$trashParam = AssetHelper::getTrashUrlParameter();
```

## Related Classes
- [FileHelper](FileHelper.md) - For file operations and validation
- [FrontHelper](FrontHelper.md) - Legacy frontend utilities (contains deprecated asset methods)
