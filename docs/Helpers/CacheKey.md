# CacheKey Documentation

## Overview
The `CacheKey` class provides centralized cache key generation with consistent naming conventions across the CRM application. It ensures cache keys follow a standardized format and prevents key collisions.

## Purpose
- Generate consistent cache key patterns for different data types
- Provide centralized key management to avoid conflicts
- Support user-specific and global cache keys
- Maintain namespacing for different functional areas (users, roles, modules, etc.)

## Key Features
- ✅ **Consistent Naming** - Standardized key format across application
- ✅ **Namespace Organization** - Logical grouping of cache keys
- ✅ **User-Specific Keys** - Dynamic user-based cache keys
- ✅ **Constants for Static Keys** - Predefined keys for common data
- ✅ **Collision Prevention** - Unique key generation patterns

## Constants (Static Cache Keys)

### Role & Permission Keys
```php
CacheKey::ALL_ROLES_WITH_DEVELOPER    // 'crm:roles:all:withDevelopers'
CacheKey::PARENT_ROLES                // 'crm:roles:parents'
CacheKey::USER_MY_PERMISSIONS         // 'crm:users:myPermissions:'
CacheKey::USER_STAFF_ROLES            // 'crm:users:staffRoles:'
CacheKey::USER_STAFF_SUB_ROLES        // 'crm:users:staffSubRoles:'
CacheKey::USER_STAFF_USERS            // 'crm:users:staffUsers:'
CacheKey::USER_SUB_STAFF_USERS        // 'crm:users:subStaffUsers:'
```

### Module & Schema Keys
```php
CacheKey::SEARCHABLE_MODULES          // 'crm:modules:searchable'
CacheKey::SCHEMA_TABLES               // 'schema:tables'
```

### List Options Keys
```php
CacheKey::LIST_OPTION_LIST            // 'listOptions:list:'
CacheKey::LIST_OPTION_SCOPE           // 'listOptions:scope:'
```

## Dynamic Methods

### User-Related Keys

#### `userMails(string $cacheKey): string`
Generates cache key for user email data.

**Usage:**
```php
$key = CacheKey::userMails('inbox_summary');
// Output: "crm:users:mails:inbox_summary"

$key = CacheKey::userMails($userId . '_sent');
// Output: "crm:users:mails:123_sent"
```

#### `settingValue(string $settingValue): string`
Generates cache key for user setting values.

**Usage:**
```php
$key = CacheKey::settingValue('theme_preference');
// Output: "crm:users:settings:theme_preference:value"

$key = CacheKey::settingValue('notification_enabled');
// Output: "crm:users:settings:notification_enabled:value"
```

#### `userUnreadNotifications(?int $userId = null): string`
Generates cache key for user unread notifications count.

**Usage:**
```php
// Current user's notifications
$key = CacheKey::userUnreadNotifications();
// Output: "crm:users:notifications:unread:123" (current user ID)

// Specific user's notifications
$key = CacheKey::userUnreadNotifications(456);
// Output: "crm:users:notifications:unread:456"
```

### Schema-Related Keys

#### `schemaTableColumnsListing(string $tableName): string`
Generates cache key for table column listings.

**Usage:**
```php
$key = CacheKey::schemaTableColumnsListing('users');
// Output: "schema:table:columns:listing:users"

$key = CacheKey::schemaTableColumnsListing('products');
// Output: "schema:table:columns:listing:products"
```

#### `schemaTableColumns(string $tableName): string`
Generates cache key for detailed table column information.

**Usage:**
```php
$key = CacheKey::schemaTableColumns('orders');
// Output: "schema:table:columns:orders"
```

#### `availableModuleColumns(string $module): string`
Generates cache key for module column configurations.

**Usage:**
```php
$key = CacheKey::availableModuleColumns('users');
// Output: "module:columns:available:users"

$key = CacheKey::availableModuleColumns('crm_products');
// Output: "module:columns:available:crm_products"
```

## Use Cases

### 1. User Permission Caching
```php
// Service class
public function getUserPermissions($userId)
{
    $cacheKey = CacheKey::USER_MY_PERMISSIONS . $userId;
    
    return Cache::remember($cacheKey, 3600, function() use ($userId) {
        return User::find($userId)->getAllPermissions()->pluck('name');
    });
}
```

### 2. Settings Management
```php
public function getUserSetting($setting, $default = null)
{
    $cacheKey = CacheKey::settingValue($setting);
    
    return Cache::remember($cacheKey, 1800, function() use ($setting, $default) {
        return Setting::where('key', $setting)->value('value') ?? $default;
    });
}
```

### 3. Notification System
```php
public function getUnreadNotificationCount($userId = null)
{
    $cacheKey = CacheKey::userUnreadNotifications($userId);
    
    return Cache::remember($cacheKey, 300, function() use ($userId) {
        $userId = $userId ?? auth()->id();
        return Notification::where('user_id', $userId)
            ->whereNull('read_at')
            ->count();
    });
}
```

### 4. Email Management
```php
public function getUserEmailSummary($userId)
{
    $cacheKey = CacheKey::userMails("summary_{$userId}");
    
    return Cache::remember($cacheKey, 600, function() use ($userId) {
        return [
            'inbox_count' => $this->getInboxCount($userId),
            'unread_count' => $this->getUnreadCount($userId),
            'sent_count' => $this->getSentCount($userId)
        ];
    });
}
```

### 5. Role-Based Data
```php
public function getAllRolesWithDeveloper()
{
    return Cache::remember(CacheKey::ALL_ROLES_WITH_DEVELOPER, 7200, function() {
        return Role::with('permissions')
            ->where('name', '!=', 'super-admin')
            ->orWhere('name', 'developer')
            ->get();
    });
}
```

## Advanced Usage Patterns

### Cache Invalidation by Pattern
```php
public function clearUserCache($userId)
{
    $patterns = [
        CacheKey::USER_MY_PERMISSIONS . $userId,
        CacheKey::userUnreadNotifications($userId),
        CacheKey::userMails("summary_{$userId}"),
        CacheKey::userMails("inbox_{$userId}"),
    ];
    
    foreach ($patterns as $pattern) {
        Cache::forget($pattern);
    }
}
```

### Bulk Key Generation
```php
public function generateSchemaKeys($tables)
{
    $keys = [];
    foreach ($tables as $table) {
        $keys[] = CacheKey::schemaTableColumnsListing($table);
        $keys[] = CacheKey::schemaTableColumns($table);
    }
    return $keys;
}
```

### Dynamic Key Building
```php
public function getListOptionKey($listType, $scope = null)
{
    if ($scope) {
        return CacheKey::LIST_OPTION_SCOPE . "{$listType}:{$scope}";
    }
    return CacheKey::LIST_OPTION_LIST . $listType;
}
```

## Key Naming Conventions

### Pattern Structure
```
namespace:entity:action:identifier
```

### Examples
- `crm:users:permissions:123` - User 123's permissions
- `schema:table:columns:users` - Users table columns
- `module:columns:available:products` - Products module columns
- `listOptions:list:countries` - Countries list options

### Best Practices for New Keys

1. **Use Consistent Namespacing**
   ```php
   // Good
   'crm:users:settings:theme'
   'crm:roles:permissions:admin'
   
   // Avoid
   'user_theme_setting'
   'admin_role_perms'
   ```

2. **Include Entity Identifiers**
   ```php
   // Good - specific to user
   CacheKey::userUnreadNotifications(123)
   
   // Avoid - too generic
   'unread_notifications'
   ```

3. **Use Descriptive Action Words**
   ```php
   // Good
   'crm:users:notifications:unread:123'
   'crm:users:settings:theme:value'
   
   // Less clear
   'crm:users:data:123'
   ```

## Integration with CacheHelper

```php
// CacheKey and CacheHelper work together
public function getCachedUserData($userId)
{
    $permissionsKey = CacheKey::USER_MY_PERMISSIONS . $userId;
    $notificationsKey = CacheKey::userUnreadNotifications($userId);
    
    $permissions = CacheHelper::getCachedKeyData($permissionsKey, function() use ($userId) {
        return $this->loadUserPermissions($userId);
    });
    
    $notifications = CacheHelper::getCachedKeyData($notificationsKey, function() use ($userId) {
        return $this->loadUnreadNotifications($userId);
    }, now()->addMinutes(5));
    
    return compact('permissions', 'notifications');
}
```

## Cache Key Management

### Listing All Keys
```php
public function getAllCacheKeys()
{
    $reflection = new ReflectionClass(CacheKey::class);
    $constants = $reflection->getConstants();
    
    return array_values($constants);
}
```

### Key Validation
```php
public function isValidCacheKey($key)
{
    // Check if key follows naming convention
    return preg_match('/^[a-zA-Z]+:[a-zA-Z]+:[a-zA-Z]+(:[a-zA-Z0-9_]+)?$/', $key);
}
```

## Performance Benefits

- **Consistency**: Reduces cache key conflicts and naming issues
- **Maintainability**: Centralized key management makes updates easier
- **Debugging**: Standardized naming helps identify cache entries
- **Collision Prevention**: Namespacing prevents accidental overwrites

## Related Classes
- [CacheHelper](CacheHelper.md) - Uses CacheKey for consistent key generation
- Laravel's Cache facade - CacheKey integrates with Laravel's caching system
