# FrontHelper Documentation

## Overview
The `FrontHelper` class is the main frontend utility class for datatable column rendering and display formatting. It serves as a bridge between your data and user interface, handling complex column types and rendering logic.

## Purpose
- Render datatable columns based on configuration and data types
- Handle complex relationship rendering (nested, lookup, morphic)
- Process various display types (images, dates, currencies, etc.)
- Maintain backward compatibility with legacy methods

## Status
⚠️ **Legacy Class** - This class is marked as deprecated and is being gradually refactored into smaller, more focused helper classes. New development should use the specialized helpers:
- [AssetHelper](AssetHelper.md) for asset management
- [DateHelper](DateHelper.md) for date formatting  
- [DatatableHelper](DatatableHelper.md) for datatable rendering
- [HtmlHelper](HtmlHelper.md) for HTML utilities

## Key Features
- ✅ **25+ Column Types** - Supports extensive column display types
- ✅ **Relationship Handling** - Nested relationships and complex lookups
- ✅ **JSON Array Support** - Processes JSON data automatically
- ✅ **Configuration-Driven** - Uses column configuration for rendering decisions
- ✅ **Backward Compatible** - Legacy methods still functional

## Main Method

### `datatableColumn(array $col, ?object $row, ?int $textLimit = null, bool $withTrashed = false, ?string $colName = null, ?string $datatableType = null, array $rowLookupRelationData = []): string`

The primary method for rendering datatable columns based on configuration.

**Parameters:**
- `$col` (array) - Column configuration with display rules
- `$row` (object|null) - Data row object
- `$textLimit` (int|null) - Character limit for text truncation
- `$withTrashed` (bool) - Include soft-deleted records
- `$colName` (string|null) - Override column name
- `$datatableType` (string|null) - Type of datatable ('report', etc.)
- `$rowLookupRelationData` (array) - Preloaded lookup data

**Returns:**
- `string` - Rendered HTML for the column

## Supported Column Types

### User Interface Types
```php
// Username card with avatar and email
'show_in_index_type' => 'username_card'

// Simple URL link
'show_in_index_type' => 'url'

// Email link with mailto
'show_in_index_type' => 'email'

// Phone number with optional WhatsApp integration
'show_in_index_type' => 'phone'
```

### Media and Files
```php
// Image display with lightbox
'show_in_index_type' => 'image' // or 'photo', 'avatar'

// File download link
'show_in_index_type' => 'file_url'

// External link with new tab
'show_in_index_type' => 'external_link'
```

### Relationships
```php
// Related model link
'show_in_index_type' => 'related_to'

// Simple nested relationship
'show_in_index_type' => 'nested'

// Nested relationship with URL
'show_in_index_type' => 'nested_with_url'

// Lookup relationship
'show_in_index_type' => 'lookup'

// Lookup with URL
'show_in_index_type' => 'lookup_with_url'

// Polymorphic relationship
'show_in_index_type' => 'nested_multiclass'

// Morph many relationship
'show_in_index_type' => 'morphMany'
```

### Data Types
```php
// Date formatting
'show_in_index_type' => 'date'

// DateTime formatting  
'show_in_index_type' => 'datetime'

// Time formatting
'show_in_index_type' => 'time'

// Status dropdown values
'show_in_index_type' => 'status'

// Currency with symbol
'show_in_index_type' => 'currency'

// Number formatting
'show_in_index_type' => 'numeric' // or 'number', 'number_format'

// Multiple selection display
'show_in_index_type' => 'multi_select'

// Plain text (default)
'show_in_index_type' => 'text' // or 'long_text'
```

## Usage Examples

### Basic Column Rendering
```php
// Simple text column
$config = [
    'name' => 'title',
    'slug' => 'title',
    'show_in_index_type' => 'text'
];

$html = FrontHelper::datatableColumn($config, $record, 50);
```

### User Column with Card
```php
$config = [
    'name' => 'user.name',
    'slug' => 'users.name', 
    'show_in_index_type' => 'username_card',
    'relation_name' => 'user'
];

$html = FrontHelper::datatableColumn($config, $record);
// Renders: Avatar + Name Link + Email
```

### Currency Column
```php
$config = [
    'name' => 'amount',
    'slug' => 'amount',
    'show_in_index_type' => 'currency',
    'numeric_decimal' => 2
];

$html = FrontHelper::datatableColumn($config, $record);
// Output: "1,234.56 USD"
```

### Relationship Lookup
```php
$config = [
    'name' => 'category_id',
    'slug' => 'category_id',
    'show_in_index_type' => 'lookup_with_url',
    'relation_name' => 'category',
    'relation_name_class' => 'App\Models\Category'
];

$html = FrontHelper::datatableColumn($config, $record);
// Output: Linked category name
```

### Date Column
```php
$config = [
    'name' => 'created_at',
    'slug' => 'created_at',
    'show_in_index_type' => 'datetime'
];

$html = FrontHelper::datatableColumn($config, $record);
// Output: "23/01/2024 02:30 PM"
```

## Column Configuration Structure

```php
$columnConfig = [
    // Basic identification
    'name' => 'field_name',           // Database field name
    'slug' => 'table.field_name',     // Full field identifier
    
    // Display configuration
    'show_in_index_type' => 'text',   // Rendering type
    'numeric_decimal' => 2,           // Decimal places for numbers
    'textLimit' => 50,               // Character limit
    
    // Relationship configuration
    'relation_name' => 'user',        // Eloquent relationship name
    'relation_name_class' => 'App\Models\User', // Related model class
    'field_type' => 'lookup',         // Field type classification
    
    // Additional options
    'status_list' => [...],           // For status type columns
    'related_list' => [...],          // For multi-select columns
    'json_attrs' => true,             // Enable JSON attribute extraction
    'before_addon' => '$',            // Prefix addon
    'after_addon' => 'USD',           // Suffix addon
];
```

## Advanced Features

### Nested Relationships
Supports deep relationship traversal:
```php
// Renders user.profile.company.name
$config['name'] = 'user.profile.company.name';
```

### JSON Array Processing
Automatically handles JSON arrays in data:
```php
// If field contains: ["item1", "item2", "item3"]
// Renders as formatted list or processes each item
```

### Conditional Display
Handles soft-deleted relationships:
```php
$html = FrontHelper::datatableColumn($config, $record, 50, true); // Include trashed
```

## Legacy Methods (Deprecated)

These methods are maintained for backward compatibility but should be replaced:

```php
// Asset methods - use AssetHelper instead
FrontHelper::assetVersion($path);        // → AssetHelper::versionedAsset($path)
FrontHelper::version($file);             // → AssetHelper::getFileVersion($file)
FrontHelper::getUrlTrashParams();        // → AssetHelper::getTrashUrlParameter()

// Date methods - use DateHelper instead  
FrontHelper::showDate($date);            // → DateHelper::formatDate($date)
FrontHelper::showDatetime($datetime);    // → DateHelper::formatDateTime($datetime)
FrontHelper::showTime($time);            // → DateHelper::formatTime($time)

// HTML methods - use HtmlHelper/DatatableHelper instead
FrontHelper::isJson($string);            // → HtmlHelper::isJson($string)
FrontHelper::showTooltipDatatableLink(); // → DatatableHelper::createTooltipLink()
FrontHelper::showTooltipDatatableText(); // → DatatableHelper::createTooltipText()
```

## Migration Strategy

### Phase 1: Use New Helpers
```php
// Instead of FrontHelper for new code
$date = DateHelper::formatDate($record->created_at);
$asset = AssetHelper::versionedAsset('/css/app.css');
$link = DatatableHelper::createTooltipLink($url, $text, 50);
```

### Phase 2: Update Column Configs
```php
// Update datatable configurations to use new rendering
$columns = [
    [
        'name' => 'created_at',
        'show_in_index_type' => 'datetime', // Still uses FrontHelper internally
        'formatter' => fn($value) => DateHelper::formatDateTime($value) // Custom override
    ]
];
```

### Phase 3: Custom Rendering
```php
// For complex cases, create custom renderers
public function renderCustomColumn($config, $row)
{
    switch ($config['show_in_index_type']) {
        case 'custom_user_card':
            return DatatableHelper::createUsernameCard($row->user, route('users.show', $row->user), $row->user->name);
            
        case 'custom_status':
            return HtmlHelper::badge($row->status, $this->getStatusType($row->status));
            
        default:
            return FrontHelper::datatableColumn($config, $row);
    }
}
```

## Best Practices

1. **Use Specific Helpers for New Code**
   ```php
   // Good - specific and maintainable
   $date = DateHelper::formatDateTime($value);
   
   // Avoid for new code - monolithic
   $date = FrontHelper::showDatetime($value);
   ```

2. **Optimize Lookup Data**
   ```php
   // Preload lookup data to avoid N+1 queries
   $lookupData = $this->preloadRelationshipData($records);
   foreach ($records as $record) {
       $html = FrontHelper::datatableColumn($config, $record, 50, false, null, null, $lookupData);
   }
   ```

3. **Configure Sensible Text Limits**
   ```php
   // Different limits for different content types
   $titleLimit = 30;
   $descriptionLimit = 100;
   $emailLimit = 50;
   ```

## Performance Considerations

- **Relationship Loading**: Use eager loading to prevent N+1 queries
- **Lookup Data**: Preload relationship data when rendering multiple rows
- **Text Processing**: Set appropriate text limits to avoid excessive truncation processing
- **JSON Handling**: JSON validation and processing adds overhead for large datasets

## Error Handling
- Returns empty string for null/invalid rows
- Handles missing relationships gracefully
- Logs errors for JSON processing failures
- Validates class existence before instantiation

## Related Classes
- [AssetHelper](AssetHelper.md) - Asset management (replaces asset methods)
- [DateHelper](DateHelper.md) - Date formatting (replaces date methods)
- [DatatableHelper](DatatableHelper.md) - UI components (replaces tooltip methods)
- [HtmlHelper](HtmlHelper.md) - HTML utilities (replaces HTML methods)
