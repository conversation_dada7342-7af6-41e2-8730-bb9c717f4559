# LangHelper Documentation

## Overview
The `LangHelper` class provides internationalization (i18n) and localization utilities for the CRM application, supporting multi-language functionality with RTL (Right-to-Left) language support.

## Purpose
- Generate localized URLs with language prefixes
- Handle RTL/LTR language direction detection
- Manage language configurations and metadata
- Export translation files as JSON for frontend use
- Provide language-specific utilities and helpers

## Key Features
- ✅ **Multi-Language Support** - Arabic and English languages
- ✅ **RTL Support** - Right-to-left language handling
- ✅ **URL Localization** - Language-aware URL generation
- ✅ **Translation Export** - JSON export for frontend frameworks
- ✅ **Language Metadata** - Flags, native names, and direction info

## Methods

### URL Localization

#### `getLocalizedURL(string $path): string`
Generates localized URLs with proper formatting.

**Parameters:**
- `$path` (string) - Path to localize

**Returns:**
- `string` - Localized URL path

**Usage:**
```php
// Basic URL localization
$url = LangHelper::getLocalizedURL('users/create');
// Output: "/users/create"

$url = LangHelper::getLocalizedURL('/dashboard/reports');
// Output: "/dashboard/reports"

// Use in route generation
$profileUrl = LangHelper::getLocalizedURL("profile/{$user->id}");
$editUrl = LangHelper::getLocalizedURL("users/{$user->id}/edit");
```

### Language Direction

#### `langDir(?string $lang = null): string`
Determines text direction (LTR/RTL) for a language.

**Usage:**
```php
// Current language direction
$direction = LangHelper::langDir();
// Output: "ltr" or "rtl"

// Specific language direction
$arabicDir = LangHelper::langDir('ar');
// Output: "rtl"

$englishDir = LangHelper::langDir('en');
// Output: "ltr"

// Use in CSS classes
$cssClass = 'content-' . LangHelper::langDir();
// Output: "content-ltr" or "content-rtl"
```

#### `isRTL(?string $lang = null): bool`
Checks if a language uses RTL direction.

**Usage:**
```php
// Check current language
if (LangHelper::isRTL()) {
    // Apply RTL-specific styling
    $textAlign = 'text-right';
} else {
    $textAlign = 'text-left';
}

// Check specific language
$isArabicRTL = LangHelper::isRTL('ar');  // true
$isEnglishRTL = LangHelper::isRTL('en'); // false
```

#### `LangRtlArray(): array`
Returns array of RTL language codes.

**Usage:**
```php
$rtlLanguages = LangHelper::LangRtlArray();
// Output: ['ar', 'fa']

// Use for validation
if (in_array($userLang, LangHelper::LangRtlArray())) {
    // Handle RTL language
}
```

### Language Configuration

#### `langs(): array`
Returns complete language configuration with metadata.

**Usage:**
```php
$languages = LangHelper::langs();
// Output:
// [
//     'ar' => [
//         'native' => 'العربية',
//         'name' => 'arabic',
//         'slug' => 'ar',
//         'dir' => 'rtl',
//         'flag' => 'sa'
//     ],
//     'en' => [
//         'native' => 'English',
//         'name' => 'english', 
//         'slug' => 'en',
//         'dir' => 'ltr',
//         'flag' => 'us'
//     ]
// ]

// Access specific language data
$arabicConfig = LangHelper::langs()['ar'];
$nativeName = $arabicConfig['native']; // "العربية"
$flag = $arabicConfig['flag'];         // "sa"
```

#### `formLang(): string`
Gets language from request or falls back to application locale.

**Usage:**
```php
// Get language for form processing
$formLanguage = LangHelper::formLang();

// Use in form handling
public function store(Request $request)
{
    $lang = LangHelper::formLang();
    $data = $request->validate([
        'title_' . $lang => 'required|string'
    ]);
}
```

### Translation Export

#### `getTransFileJson(?string $file = null): void`
Exports translation files as JSON for frontend frameworks.

**Usage:**
```php
// Export all translations
LangHelper::getTransFileJson();
// Outputs JSON with all translation files

// Use in API endpoint
Route::get('/api/translations', function() {
    return response()->json([], 200, [], JSON_UNESCAPED_UNICODE)
        ->setCallback(request()->callback);
    LangHelper::getTransFileJson();
});
```

## Use Cases

### 1. Language Switcher Component
```php
public function getLanguageSwitcher()
{
    $languages = LangHelper::langs();
    $currentLang = app()->getLocale();
    
    $switcher = [];
    foreach ($languages as $code => $config) {
        $switcher[] = [
            'code' => $code,
            'name' => $config['native'],
            'flag' => $config['flag'],
            'active' => $code === $currentLang,
            'url' => LangHelper::getLocalizedURL(request()->path())
        ];
    }
    
    return $switcher;
}
```

### 2. RTL-Aware Layout
```blade
<!-- In Blade layout -->
<html lang="{{ app()->getLocale() }}" dir="{{ LangHelper::langDir() }}">
<head>
    @if(LangHelper::isRTL())
        <link href="{{ asset('css/app-rtl.css') }}" rel="stylesheet">
    @else
        <link href="{{ asset('css/app-ltr.css') }}" rel="stylesheet">
    @endif
</head>
<body class="lang-{{ app()->getLocale() }} dir-{{ LangHelper::langDir() }}">
```

### 3. Multi-Language Form Processing
```php
public function store(Request $request)
{
    $lang = LangHelper::formLang();
    $languages = array_keys(LangHelper::langs());
    
    $rules = [];
    foreach ($languages as $langCode) {
        $rules["title_{$langCode}"] = 'required|string|max:255';
        $rules["description_{$langCode}"] = 'nullable|string';
    }
    
    $validated = $request->validate($rules);
    
    // Process multi-language data
    $translations = [];
    foreach ($languages as $langCode) {
        $translations[$langCode] = [
            'title' => $validated["title_{$langCode}"],
            'description' => $validated["description_{$langCode}"] ?? null
        ];
    }
    
    return $this->saveWithTranslations($translations);
}
```

### 4. Localized Navigation
```php
public function getLocalizedNavigation()
{
    $menuItems = [
        'dashboard' => __('Dashboard'),
        'users' => __('Users'),
        'products' => __('Products'),
        'reports' => __('Reports')
    ];
    
    $navigation = [];
    foreach ($menuItems as $route => $title) {
        $navigation[] = [
            'title' => $title,
            'url' => LangHelper::getLocalizedURL($route),
            'icon' => $this->getMenuIcon($route)
        ];
    }
    
    return $navigation;
}
```

### 5. Frontend Translation API
```php
// API Controller
public function getTranslations(Request $request)
{
    $lang = $request->get('lang', app()->getLocale());
    
    // Set temporary locale
    $originalLocale = app()->getLocale();
    app()->setLocale($lang);
    
    ob_start();
    LangHelper::getTransFileJson();
    $translations = ob_get_clean();
    
    // Restore original locale
    app()->setLocale($originalLocale);
    
    return response($translations)
        ->header('Content-Type', 'application/json')
        ->header('Cache-Control', 'max-age=3600');
}
```

### 6. Dynamic Content Direction
```php
public function renderContent($content, $lang = null)
{
    $direction = LangHelper::langDir($lang);
    $isRTL = LangHelper::isRTL($lang);
    
    $cssClasses = [
        'content',
        'content-' . $direction,
        $isRTL ? 'rtl-content' : 'ltr-content'
    ];
    
    return sprintf(
        '<div class="%s" dir="%s">%s</div>',
        implode(' ', $cssClasses),
        $direction,
        $content
    );
}
```

## Advanced Usage

### Language-Specific Routing
```php
// In RouteServiceProvider
public function boot()
{
    Route::macro('localized', function ($path, $action) {
        $languages = array_keys(LangHelper::langs());
        
        foreach ($languages as $lang) {
            $localizedPath = $lang . '/' . ltrim($path, '/');
            Route::get($localizedPath, $action)->name($lang . '.' . $path);
        }
        
        // Default route without language prefix
        Route::get($path, $action)->name($path);
    });
}

// Usage
Route::localized('users', [UserController::class, 'index']);
// Creates: /en/users, /ar/users, /users
```

### RTL-Aware CSS Generation
```php
public function generateRTLCSS($cssContent)
{
    if (!LangHelper::isRTL()) {
        return $cssContent;
    }
    
    // Convert LTR CSS rules to RTL
    $rtlRules = [
        'margin-left' => 'margin-right',
        'margin-right' => 'margin-left',
        'padding-left' => 'padding-right', 
        'padding-right' => 'padding-left',
        'float: left' => 'float: right',
        'float: right' => 'float: left',
        'text-align: left' => 'text-align: right',
        'text-align: right' => 'text-align: left'
    ];
    
    return str_replace(array_keys($rtlRules), array_values($rtlRules), $cssContent);
}
```

### Language-Aware Validation
```php
public function getValidationRules($lang = null)
{
    $lang = $lang ?? LangHelper::formLang();
    $isRTL = LangHelper::isRTL($lang);
    
    $rules = [
        'email' => 'required|email',
        'phone' => 'required|string'
    ];
    
    if ($isRTL) {
        // RTL-specific validation rules
        $rules['name'] = 'required|regex:/^[\p{Arabic}\s]+$/u';
    } else {
        // LTR-specific validation rules
        $rules['name'] = 'required|regex:/^[a-zA-Z\s]+$/';
    }
    
    return $rules;
}
```

## Best Practices

1. **Cache Language Configurations**
   ```php
   public function getCachedLanguages()
   {
       return Cache::remember('app_languages', 3600, function() {
           return LangHelper::langs();
       });
   }
   ```

2. **Use Language Fallbacks**
   ```php
   public function getLocalizedText($key, $lang = null)
   {
       $lang = $lang ?? app()->getLocale();
       
       // Try specific language first
       $text = trans($key, [], $lang);
       
       // Fallback to English if translation missing
       if ($text === $key && $lang !== 'en') {
           $text = trans($key, [], 'en');
       }
       
       return $text;
   }
   ```

3. **Validate Language Codes**
   ```php
   public function isValidLanguage($lang)
   {
       return array_key_exists($lang, LangHelper::langs());
   }
   ```

## Integration with Frontend

### Vue.js/React Integration
```javascript
// Frontend language detection
const getLanguageDirection = () => {
    return document.documentElement.dir || 'ltr';
};

const isRTL = () => {
    return getLanguageDirection() === 'rtl';
};

// Apply RTL-specific styles
if (isRTL()) {
    document.body.classList.add('rtl');
}
```

### CSS Integration
```css
/* Language-specific styles */
.dir-rtl {
    text-align: right;
}

.dir-ltr {
    text-align: left;
}

.lang-ar .font-family {
    font-family: 'Noto Sans Arabic', sans-serif;
}

.lang-en .font-family {
    font-family: 'Inter', sans-serif;
}
```

## Related Classes
- Laravel's Localization system - LangHelper integrates with Laravel's i18n features
- Translation files in `resources/lang/` directories
- Frontend frameworks - Uses exported JSON translations
