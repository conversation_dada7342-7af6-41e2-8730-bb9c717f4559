hange# ModuleHelper Documentation

## Overview
The `ModuleHelper` class provides utilities for managing CRM modules, their relationships, and dynamic class resolution. It serves as a central point for module configuration and relationship mapping across the CRM system.

## Purpose
- Resolve model class names from attribute relationships
- Map module names to their canonical forms
- Handle dynamic relationship detection
- Process search queries and request parameters
- Provide module configuration management

## Key Features
- ✅ **Dynamic Class Resolution** - Maps field names to model classes
- ✅ **Relationship Detection** - Identifies relationships from field attributes
- ✅ **Module Mapping** - Handles module name variations and aliases
- ✅ **Request Processing** - Extracts search terms and query parameters
- ✅ **Configuration Management** - Manages module configurations

## Methods

### Relationship Resolution

#### `getRelatedClass(string $attribute, $row = null): ?string`
Resolves the model class name based on a field attribute.

**Parameters:**
- `$attribute` (string) - Field attribute name (e.g., 'user_id', 'category_id')
- `$row` (object|null) - Optional row object for dynamic resolution

**Returns:**
- `string|null` - Fully qualified model class name

**Usage:**
```php
// Standard ID field resolution
$userClass = ModuleHelper::getRelatedClass('user_id');
// Output: "App\Models\User"

$categoryClass = ModuleHelper::getRelatedClass('category_id');
// Output: "App\Models\Category"

$statusClass = ModuleHelper::getRelatedClass('status_id');
// Output: "App\Models\Status"

// Dynamic resolution with row context
$ownerClass = ModuleHelper::getRelatedClass('owner_id', $record);
// Output: Actual class of the owner relationship

// Audit fields
$createdByClass = ModuleHelper::getRelatedClass('created_by');
// Output: "App\Models\User"
```

**Supported Mappings:**
```php
'category_id'   => 'App\Models\Category'
'brand_id'      => 'App\Models\Brand'
'status_id'     => 'App\Models\Status'
'inventory_id'  => 'App\Models\Inventory'
'office_id'     => 'App\Models\Office'
'currency_id'   => 'App\Models\Currency'
'user_id'       => 'App\Models\User'
'created_by'    => 'App\Models\User'
'modified_by'   => 'App\Models\User'
'deleted_by'    => 'App\Models\User'
'owner_id'      => Dynamic (based on row context)
```

#### `getRelated(string $attribute): ?string`
Extracts relationship name from attribute field.

**Usage:**
```php
$relation = ModuleHelper::getRelated('user_id');
// Output: "user"

$relation = ModuleHelper::getRelated('category_id');
// Output: "category"

$relation = ModuleHelper::getRelated('name');
// Output: null (not an ID field)
```

### Module Management

#### `getRelatedModuleName(string $moduleName): string`
Maps module name variations to their canonical forms.

**Usage:**
```php
// Activity module variations
$module = ModuleHelper::getRelatedModuleName('open_activities');
// Output: "activities"

$module = ModuleHelper::getRelatedModuleName('closed_activities');
// Output: "activities"

$module = ModuleHelper::getRelatedModuleName('users');
// Output: "users" (no change)
```

### Request Processing

#### `getRequestTerm($request): ?string`
Extracts search term from request query parameters.

**Usage:**
```php
// From a search request with query structure
// Request: ?query[term]=john+doe
$term = ModuleHelper::getRequestTerm($request);
// Output: "john doe"

// Use in search functionality
public function search(Request $request)
{
    $searchTerm = ModuleHelper::getRequestTerm($request);
    
    if ($searchTerm) {
        return User::where('name', 'like', "%{$searchTerm}%")->get();
    }
    
    return collect();
}
```

## Use Cases

### 1. Dynamic Form Field Generation
```php
public function generateFormField($fieldName, $fieldConfig)
{
    $relatedClass = ModuleHelper::getRelatedClass($fieldName);
    
    if ($relatedClass) {
        // Generate dropdown from related model
        $options = $relatedClass::pluck('name', 'id');
        return $this->createSelectField($fieldName, $options);
    }
    
    return $this->createTextField($fieldName);
}
```

### 2. Dynamic Relationship Loading
```php
public function loadDynamicRelationships($model, $fields)
{
    $relationships = [];
    
    foreach ($fields as $field) {
        $relationName = ModuleHelper::getRelated($field);
        $relatedClass = ModuleHelper::getRelatedClass($field);
        
        if ($relationName && $relatedClass) {
            $relationships[] = $relationName;
        }
    }
    
    return $model->with($relationships);
}
```

### 3. Search Functionality
```php
public function performSearch(Request $request, $modelClass)
{
    $searchTerm = ModuleHelper::getRequestTerm($request);
    
    if (!$searchTerm) {
        return $modelClass::paginate();
    }
    
    $query = $modelClass::query();
    
    // Search in related models
    $searchableFields = $this->getSearchableFields($modelClass);
    foreach ($searchableFields as $field) {
        $relatedClass = ModuleHelper::getRelatedClass($field);
        $relationName = ModuleHelper::getRelated($field);
        
        if ($relatedClass && $relationName) {
            $query->orWhereHas($relationName, function($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%");
            });
        }
    }
    
    return $query->paginate();
}
```

### 4. API Resource Generation
```php
public function generateApiResource($model, $includeRelations = true)
{
    $data = $model->toArray();
    
    if ($includeRelations) {
        foreach ($data as $key => $value) {
            if (str_ends_with($key, '_id')) {
                $relationName = ModuleHelper::getRelated($key);
                $relatedClass = ModuleHelper::getRelatedClass($key);
                
                if ($relationName && $relatedClass && $model->{$relationName}) {
                    $data[$relationName] = [
                        'id' => $model->{$relationName}->id,
                        'name' => $model->{$relationName}->name ?? $model->{$relationName}->title
                    ];
                }
            }
        }
    }
    
    return $data;
}
```

### 5. Validation Rule Generation
```php
public function generateValidationRules($fields)
{
    $rules = [];
    
    foreach ($fields as $field => $config) {
        if (str_ends_with($field, '_id')) {
            $relatedClass = ModuleHelper::getRelatedClass($field);
            
            if ($relatedClass) {
                $tableName = (new $relatedClass)->getTable();
                $rules[$field] = "nullable|exists:{$tableName},id";
            }
        } else {
            $rules[$field] = $config['validation'] ?? 'nullable|string';
        }
    }
    
    return $rules;
}
```

### 6. Module Activity Tracking
```php
public function trackActivity($action, $moduleName, $recordId)
{
    $canonicalModule = ModuleHelper::getRelatedModuleName($moduleName);
    
    Activity::create([
        'action' => $action,
        'module' => $canonicalModule,
        'record_id' => $recordId,
        'user_id' => auth()->id(),
        'performed_at' => now()
    ]);
}

// Usage
$this->trackActivity('created', 'open_activities', $activity->id);
// Logs as 'activities' module
```

## Advanced Usage

### Dynamic Model Resolution
```php
public function resolveModelFromField($fieldName, $context = null)
{
    $modelClass = ModuleHelper::getRelatedClass($fieldName, $context);
    
    if (!$modelClass || !class_exists($modelClass)) {
        throw new \Exception("Model class not found for field: {$fieldName}");
    }
    
    return new $modelClass;
}

// Usage
$userModel = $this->resolveModelFromField('created_by');
$users = $userModel->active()->get();
```

### Relationship Chain Building
```php
public function buildRelationshipChain($fields)
{
    $chain = [];
    
    foreach ($fields as $field) {
        $relationName = ModuleHelper::getRelated($field);
        
        if ($relationName) {
            $chain[] = $relationName;
        }
    }
    
    return implode('.', $chain);
}

// Usage: ['user_id', 'office_id'] becomes "user.office"
```

### Module Configuration Loading
```php
public function loadModuleConfig($moduleName)
{
    $canonicalName = ModuleHelper::getRelatedModuleName($moduleName);
    
    return config("modules.{$canonicalName}", [
        'name' => $canonicalName,
        'model' => 'App\\Models\\' . Str::studly(Str::singular($canonicalName)),
        'controller' => 'App\\Http\\Controllers\\' . Str::studly($canonicalName) . 'Controller'
    ]);
}
```

## Best Practices

1. **Use for Dynamic Operations Only**
   ```php
   // Good - dynamic field processing
   $relatedClass = ModuleHelper::getRelatedClass($dynamicField);
   
   // Avoid - when relationship is known
   $user = User::find($userId); // Better than dynamic resolution
   ```

2. **Validate Class Existence**
   ```php
   $modelClass = ModuleHelper::getRelatedClass($field);
   if ($modelClass && class_exists($modelClass)) {
       // Safe to use
       $instance = new $modelClass;
   }
   ```

3. **Cache Module Configurations**
   ```php
   public function getCachedModuleConfig($module)
   {
       $canonicalName = ModuleHelper::getRelatedModuleName($module);
       
       return Cache::remember("module_config_{$canonicalName}", 3600, function() use ($canonicalName) {
           return $this->loadModuleConfiguration($canonicalName);
       });
   }
   ```

4. **Handle Edge Cases**
   ```php
   public function safeGetRelatedClass($attribute, $row = null)
   {
       try {
           return ModuleHelper::getRelatedClass($attribute, $row);
       } catch (\Exception $e) {
           Log::warning("Failed to resolve class for attribute: {$attribute}", [
               'error' => $e->getMessage()
           ]);
           return null;
       }
   }
   ```

## Integration Points

### With CacheHelper
```php
// ModuleHelper often works with CacheHelper for module data
$moduleColumns = CacheHelper::getCachedAvailableModuleColumns($moduleName);
```

### With Database Queries
```php
// Dynamic eager loading based on field analysis
$fieldsWithRelations = collect($fields)->filter(function($field) {
    return ModuleHelper::getRelated($field) !== null;
});
```

### With Form Builders
```php
// Dynamic form generation using ModuleHelper
foreach ($formFields as $field) {
    $type = ModuleHelper::getRelatedClass($field) ? 'select' : 'text';
    $form->add($field, $type);
}
```

## Related Classes
- [CacheHelper](CacheHelper.md) - Often used together for module configuration caching
- Model classes - ModuleHelper resolves to various Eloquent models
- Form builders - Uses ModuleHelper for dynamic field type detection
