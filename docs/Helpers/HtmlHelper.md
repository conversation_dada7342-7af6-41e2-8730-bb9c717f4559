# HtmlHelper Documentation

## Overview
The `HtmlHelper` class provides HTML utilities, JSON validation, and safe HTML element creation with XSS protection.

## Purpose
- Validate JSON strings safely
- Create HTML elements with proper escaping
- Generate common UI components (links, tooltips, badges)
- Prevent XSS attacks through proper escaping

## Key Features
- ✅ **XSS Protection** - Automatic HTML escaping
- ✅ **JSON Validation** - Robust JSON string validation
- ✅ **Tooltip Support** - Bootstrap-compatible tooltips
- ✅ **Flexible Attributes** - Support for custom HTML attributes

## Methods

### JSON Utilities

#### `isJson($string, bool $returnData = false)`
Validates if a string is valid JSON with enhanced cleaning.

**Parameters:**
- `$string` (mixed) - String to validate
- `$returnData` (bool) - Whether to return decoded data or just boolean

**Returns:**
- `bool|mixed` - True/false or decoded data if returnData is true

**Usage:**
```php
// Basic validation
$isValid = HtmlHelper::isJson('{"name":"<PERSON>","age":30}');
// Output: true

$isInvalid = HtmlHelper::isJson('not-json');
// Output: false

// Return decoded data
$data = HtmlHelper::isJson('{"name":"John"}', true);
// Output: ['name' => 'John']

// Handle edge cases
$numeric = HtmlHelper::isJson('123');
// Output: false (numeric strings are not considered JSON)

$cleaned = HtmlHelper::isJson('"wrapped in quotes"');
// Output: true (automatically cleans quotes)
```

### HTML Safety

#### `escape(?string $string): string`
Safely escapes HTML special characters.

**Usage:**
```php
$safe = HtmlHelper::escape('<script>alert("XSS")</script>');
// Output: "&lt;script&gt;alert(&quot;XSS&quot;)&lt;/script&gt;"

$safe = HtmlHelper::escape('Name: John & Jane');
// Output: "Name: John &amp; Jane"

// Handles null values
$safe = HtmlHelper::escape(null);
// Output: ""
```

### HTML Element Creation

#### `link(string $url, string $text, array $attributes = []): string`
Creates a safe HTML link with optional attributes.

**Usage:**
```php
// Basic link
$link = HtmlHelper::link('/dashboard', 'Dashboard');
// Output: <a href="/dashboard">Dashboard</a>

// With attributes
$link = HtmlHelper::link('/profile', 'Profile', [
    'class' => 'btn btn-primary',
    'target' => '_blank',
    'data-id' => '123'
]);
// Output: <a href="/profile" class="btn btn-primary" target="_blank" data-id="123">Profile</a>
```

#### `tooltipLink(string $url, string $text, ?string $tooltip = null, array $attributes = []): string`
Creates an HTML link with Bootstrap tooltip.

**Usage:**
```php
$link = HtmlHelper::tooltipLink('/help', 'Help', 'Get assistance');
// Output: <a href="/help" data-toggle="tooltip" title="Get assistance">Help</a>

// With custom attributes
$link = HtmlHelper::tooltipLink('/edit', 'Edit', 'Edit this record', [
    'class' => 'btn btn-sm btn-primary'
]);
```

#### `truncateWithTooltip(string $text, int $limit, array $attributes = []): string`
Truncates text and adds tooltip if text exceeds limit.

**Usage:**
```php
$short = "Hello World";
$result = HtmlHelper::truncateWithTooltip($short, 50);
// Output: "Hello World" (no truncation needed)

$long = "This is a very long text that needs to be truncated";
$result = HtmlHelper::truncateWithTooltip($long, 20);
// Output: <span data-toggle="tooltip" title="This is a very long text that needs to be truncated">This is a very lon...</span>

// With custom attributes
$result = HtmlHelper::truncateWithTooltip($long, 20, [
    'class' => 'text-muted',
    'data-placement' => 'top'
]);
```

#### `badge(string $text, string $type = 'secondary', array $attributes = []): string`
Creates a Bootstrap badge element.

**Usage:**
```php
// Basic badge
$badge = HtmlHelper::badge('New');
// Output: <span class="badge badge-secondary">New</span>

// With type
$badge = HtmlHelper::badge('Success', 'success');
// Output: <span class="badge badge-success">Success</span>

// With custom attributes
$badge = HtmlHelper::badge('5', 'primary', [
    'class' => 'badge badge-primary badge-pill',
    'data-count' => '5'
]);
```

## Use Cases

### 1. Safe Data Display
```php
// Controller
public function show(User $user)
{
    return view('users.show', [
        'safe_bio' => HtmlHelper::escape($user->bio),
        'safe_company' => HtmlHelper::escape($user->company)
    ]);
}
```

### 2. Dynamic Link Generation
```php
// In a service class
public function generateNavigationLinks(array $items): array
{
    $links = [];
    
    foreach ($items as $item) {
        $links[] = HtmlHelper::link($item['url'], $item['title'], [
            'class' => $item['active'] ? 'active' : '',
            'data-section' => $item['section']
        ]);
    }
    
    return $links;
}
```

### 3. Datatable Cell Formatting
```php
public function formatTableCell($value, $limit = 50)
{
    // Validate if it's JSON data
    if (HtmlHelper::isJson($value)) {
        $data = HtmlHelper::isJson($value, true);
        $display = implode(', ', array_keys($data));
        return HtmlHelper::truncateWithTooltip($display, $limit);
    }
    
    // Regular text truncation
    return HtmlHelper::truncateWithTooltip($value, $limit);
}
```

### 4. Status Badge Creation
```php
public function getStatusBadge($status): string
{
    $config = [
        'active' => ['text' => 'Active', 'type' => 'success'],
        'pending' => ['text' => 'Pending', 'type' => 'warning'],
        'inactive' => ['text' => 'Inactive', 'type' => 'secondary'],
        'blocked' => ['text' => 'Blocked', 'type' => 'danger']
    ];
    
    $statusConfig = $config[$status] ?? $config['inactive'];
    
    return HtmlHelper::badge($statusConfig['text'], $statusConfig['type']);
}
```

### 5. Form Helper
```blade
<!-- In Blade templates -->
<div class="form-group">
    <label for="website">Website {!! HtmlHelper::tooltipLink('#', '?', 'Enter your website URL') !!}</label>
    <input type="url" id="website" name="website" value="{{ HtmlHelper::escape($user->website) }}">
</div>
```

### 6. JSON Data Processing
```php
// API response formatting
public function formatApiResponse($data)
{
    foreach ($data as &$item) {
        if (isset($item['metadata']) && HtmlHelper::isJson($item['metadata'])) {
            $metadata = HtmlHelper::isJson($item['metadata'], true);
            $item['metadata_formatted'] = $this->formatMetadata($metadata);
        }
    }
    
    return $data;
}
```

## Advanced Usage

### Custom Tooltip Attributes
```php
$link = HtmlHelper::tooltipLink('/profile', 'View Profile', 'Click to view full profile', [
    'data-toggle' => 'tooltip',
    'data-placement' => 'top',
    'data-html' => 'true',
    'class' => 'profile-link'
]);
```

### Conditional Badge Generation
```php
public function generatePriorityBadge($priority, $count = null): string
{
    $text = ucfirst($priority);
    if ($count) {
        $text .= " ({$count})";
    }
    
    $typeMap = [
        'high' => 'danger',
        'medium' => 'warning',
        'low' => 'info'
    ];
    
    return HtmlHelper::badge($text, $typeMap[$priority] ?? 'secondary');
}
```

### Safe JSON Manipulation
```php
public function processFormData($input)
{
    if (HtmlHelper::isJson($input['preferences'])) {
        $preferences = HtmlHelper::isJson($input['preferences'], true);
        
        // Safely process each preference
        foreach ($preferences as $key => &$value) {
            $value = HtmlHelper::escape($value);
        }
        
        return json_encode($preferences);
    }
    
    return null;
}
```

## Best Practices

1. **Always Escape User Input**
   ```php
   // Good
   echo HtmlHelper::escape($userInput);
   
   // Dangerous - XSS vulnerability
   echo $userInput;
   ```

2. **Use JSON Validation**
   ```php
   // Good
   if (HtmlHelper::isJson($data)) {
       $decoded = HtmlHelper::isJson($data, true);
   }
   
   // Risky - may throw exceptions
   $decoded = json_decode($data);
   ```

3. **Prefer Helper Methods Over Manual HTML**
   ```php
   // Good
   $link = HtmlHelper::link($url, $text, $attributes);
   
   // Harder to maintain
   $link = '<a href="' . htmlspecialchars($url) . '">' . htmlspecialchars($text) . '</a>';
   ```

4. **Use Tooltips for Better UX**
   ```php
   // Provide context with tooltips
   $helpLink = HtmlHelper::tooltipLink('/help', '?', 'Click for help');
   ```

## Migration from Legacy Code

### Before (Manual Escaping)
```php
// Old way - manual and error-prone
$safe = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
$link = '<a href="' . htmlspecialchars($url) . '">' . htmlspecialchars($text) . '</a>';
```

### After (HtmlHelper)
```php
// New way - safer and cleaner
$safe = HtmlHelper::escape($text);
$link = HtmlHelper::link($url, $text);
```

### JSON Handling Migration
```php
// Before
$isJson = json_decode($string) !== null && json_last_error() === JSON_ERROR_NONE;

// After  
$isJson = HtmlHelper::isJson($string);
$data = HtmlHelper::isJson($string, true);
```

## Security Considerations

- All output is automatically escaped to prevent XSS
- JSON validation includes input sanitization
- Attribute values are properly escaped
- Null values are handled safely

## Related Classes
- [DatatableHelper](DatatableHelper.md) - Uses HtmlHelper for safe HTML generation
- [DateHelper](DateHelper.md) - Previously contained JSON validation (now moved to HtmlHelper)
- [FrontHelper](FrontHelper.md) - Uses HtmlHelper for legacy method compatibility
