   ```

## Integration with Other Systems

### DataTables.js Integration
```javascript
// Client-side DataTables configuration
$('#example').DataTable({
    columnDefs: [
        {
            targets: [0], // User column
            render: function(data, type, row) {
                // Server-side rendered HTML is displayed directly
                return data;
            }
        }
    ]
});
```

### Laravel Blade Integration
```blade
<!-- Datatable rendering -->
<table class="table">
    @foreach($records as $record)
        <tr>
            <td>{!! DatatableHelper::createUsernameCard($record->user, route('users.show', $record->user), $record->user->name, $record->user->email, 25) !!}</td>
            <td>{!! DatatableHelper::createTooltipText($record->description, 80) !!}</td>
        </tr>
    @endforeach
</table>
```

## Error Handling
- All methods gracefully handle null inputs
- JSON formatting errors are logged and return empty strings
- Missing functions (like `list_option_mark`) are checked before use
- Invalid URLs or text are properly escaped

## Performance Considerations
- Text truncation is done efficiently with <PERSON><PERSON>'s `Str::limit()`
- JSON validation is performed before processing
- HTML generation is optimized for minimal overhead
- Color mark functions are called conditionally

## Related Classes
- [HtmlHelper](HtmlHelper.md) - Used for JSON validation and HTML escaping
- [FrontHelper](FrontHelper.md) - Uses DatatableHelper for modern datatable rendering
- [DateHelper](DateHelper.md) - Often used together for date column formatting
# DatatableHelper Documentation

## Overview
The `DatatableHelper` class provides specialized utilities for datatable display formatting, HTML rendering, and user interface components.

## Purpose
- Create consistent datatable column displays
- Generate tooltip links and text with character limits
- Render username cards and user interface elements
- Handle JSON array formatting for datatables
- Process count suffixes and prefix/suffix additions

## Key Features
- ✅ **XSS Protection** - All output is properly escaped
- ✅ **Tooltip Integration** - Bootstrap-compatible tooltips
- ✅ **JSON Array Support** - Handles JSON data formatting
- ✅ **User Cards** - Rich user display components
- ✅ **Flexible Styling** - Support for color marks and custom classes

## Methods

### Link and Text Generation

#### `createTooltipLink(?string $url, ?string $text, ?int $textLimit = null, ?string $title = null, ?string $markColor = null): string`
Creates a tooltip-enabled link for datatable display.

**Parameters:**
- `$url` (string|null) - Link URL
- `$text` (string|null) - Link text
- `$textLimit` (int|null) - Character limit for text truncation
- `$title` (string|null) - Tooltip title
- `$markColor` (string|null) - Optional color mark

**Usage:**
```php
// Basic link
$link = DatatableHelper::createTooltipLink('/users/1', 'John Doe');
// Output: <a href="/users/1">John Doe</a>

// With character limit and tooltip
$link = DatatableHelper::createTooltipLink(
    '/users/1', 
    'Very Long User Name That Needs Truncation', 
    20, 
    'Click to view user profile'
);
// Output: <span data-toggle="tooltip" title="Very Long User Name That Needs Truncation">
//         <a href="/users/1" title="Click to view user profile">Very Long User Name...</a>
//         </span>

// With color mark (if list_option_mark function exists)
$link = DatatableHelper::createTooltipLink('/users/1', 'John Doe', null, null, '#ff0000');
```

#### `createTooltipText(?string $text, ?int $textLimit = null, ?string $markColor = null): string`
Creates tooltip-enabled text display for datatables.

**Usage:**
```php
// Basic text
$text = DatatableHelper::createTooltipText('Simple text');
// Output: "Simple text"

// With truncation
$text = DatatableHelper::createTooltipText(
    'This is a very long description that should be truncated', 
    30
);
// Output: <span data-toggle="tooltip" title="This is a very long description that should be truncated">
//         This is a very long descrip...
//         </span>
```

### User Interface Components

#### `createUsernameCard(object $row, string $url, string $name, ?string $email = null, ?int $textLimit = null): string`
Creates a rich username card display with avatar, name, and email.

**Usage:**
```php
$user = (object)[
    'id' => 1,
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'avatar' => 'avatar.jpg',
    'avatarPath' => '<img src="/avatars/avatar.jpg" class="avatar">'
];

$card = DatatableHelper::createUsernameCard($user, '/users/1', $user->name, $user->email);
// Output: Rich HTML card with avatar, name link, and email
```

**Generated HTML Structure:**
```html
<div class="media">
    <a class="username-card-link" href="/users/1">
        <div class="username-card-image avatar mr-1">
            <!-- Avatar image or placeholder -->
        </div>
        <div class="username-card-info media-body">
            <h6 class="media-heading mb-0">
                <a href="/users/1" title="Click to open profile">John Doe</a>
            </h6>
            <small class="username-card-email text-muted">
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </small>
        </div>
    </a>
</div>
```

### JSON and Data Processing

#### `formatJsonForDisplay(string $jsonString): string`
Formats JSON arrays into HTML lists for datatable display.

**Usage:**
```php
$json = '["Item 1", "Item 2", null, "Item 3"]';
$formatted = DatatableHelper::formatJsonForDisplay($json);
// Output: <ul class='list-group'>
//           <li>Item 1</li>
//           <li>Item 2</li>
//           <li class='text-muted'>-</li>
//           <li>Item 3</li>
//         </ul>

// Handle complex arrays
$complexJson = '[{"name": "John", "age": 30}, {"name": "Jane", "age": 25}]';
$formatted = DatatableHelper::formatJsonForDisplay($complexJson);
// Output: Formatted list with array content
```

### Column Enhancement

#### `addCountSuffix(string $column, object $row, array $colConfig): string`
Adds count suffix to column text if count attribute exists.

**Usage:**
```php
$row = (object)[
    'name' => 'Category Name',
    'category_count' => 5
];

$colConfig = ['slug' => 'category'];
$result = DatatableHelper::addCountSuffix('Category Name', $row, $colConfig);
// Output: "Category Name (5)"
```

#### `addPrefixSuffix(string $column, object $row): string`
Adds prefix and suffix to column text from row data.

**Usage:**
```php
$row = (object)[
    'prefix' => '$',
    'suffix' => ' USD'
];

$result = DatatableHelper::addPrefixSuffix('100', $row);
// Output: "$100 USD"
```

## Use Cases

### 1. Datatable Column Rendering
```php
// In a datatable service
public function renderUserColumn($user, $textLimit = 30)
{
    return DatatableHelper::createUsernameCard(
        $user, 
        route('users.show', $user->id),
        $user->name,
        $user->email,
        $textLimit
    );
}
```

### 2. Action Links in Tables
```php
public function renderActionColumn($record)
{
    $viewLink = DatatableHelper::createTooltipLink(
        route('records.show', $record->id),
        'View',
        null,
        'Click to view details'
    );
    
    $editLink = DatatableHelper::createTooltipLink(
        route('records.edit', $record->id),
        'Edit',
        null,
        'Click to edit record'
    );
    
    return $viewLink . ' ' . $editLink;
}
```

### 3. Complex Data Display
```php
public function renderMetadataColumn($metadata)
{
    if (HtmlHelper::isJson($metadata)) {
        return DatatableHelper::formatJsonForDisplay($metadata);
    }
    
    return DatatableHelper::createTooltipText($metadata, 50);
}
```

### 4. Status with Counts
```php
public function renderStatusColumn($row, $config)
{
    $status = DatatableHelper::createTooltipText($row->status_name, 20);
    return DatatableHelper::addCountSuffix($status, $row, $config);
}
```

### 5. Rich Description Display
```blade
<!-- In Blade template -->
@foreach($items as $item)
    <tr>
        <td>{!! DatatableHelper::createUsernameCard($item->user, route('users.show', $item->user), $item->user->name, $item->user->email) !!}</td>
        <td>{!! DatatableHelper::createTooltipText($item->description, 100) !!}</td>
        <td>{!! DatatableHelper::createTooltipLink(route('items.show', $item), 'View', null, 'View item details') !!}</td>
    </tr>
@endforeach
```

## Advanced Usage

### Custom Color Marks
```php
// If you have a list_option_mark function defined
public function renderPriorityColumn($priority, $color)
{
    return DatatableHelper::createTooltipText(
        ucfirst($priority), 
        null, 
        $color // Will add color mark if function exists
    );
}
```

### Conditional Link Generation
```php
public function renderNameColumn($record, $canView = true)
{
    if ($canView) {
        return DatatableHelper::createTooltipLink(
            route('records.show', $record->id),
            $record->name,
            50,
            'Click to view details'
        );
    }
    
    return DatatableHelper::createTooltipText($record->name, 50);
}
```

### JSON Array with Custom Processing
```php
public function renderTagsColumn($tagsJson)
{
    if (HtmlHelper::isJson($tagsJson)) {
        // Custom processing before display
        $tags = HtmlHelper::isJson($tagsJson, true);
        $processedTags = array_map(function($tag) {
            return ucfirst(trim($tag));
        }, $tags);
        
        return DatatableHelper::formatJsonForDisplay(json_encode($processedTags));
    }
    
    return DatatableHelper::createTooltipText($tagsJson, 50);
}
```

## Best Practices

1. **Always Use XSS Protection**
   ```php
   // Good - automatically escaped
   DatatableHelper::createTooltipText($userInput);
   
   // Dangerous - no escaping
   echo $userInput;
   ```

2. **Set Appropriate Text Limits**
   ```php
   // Good - reasonable limits for different column types
   $name = DatatableHelper::createTooltipText($name, 30);
   $description = DatatableHelper::createTooltipText($description, 100);
   
   // Avoid very short limits that cut off important info
   $email = DatatableHelper::createTooltipText($email, 10); // Too short
   ```

3. **Provide Meaningful Tooltips**
   ```php
   // Good - descriptive tooltip
   DatatableHelper::createTooltipLink($url, 'Edit', null, 'Click to edit this record');
   
   // Less helpful
   DatatableHelper::createTooltipLink($url, 'Edit', null, 'Edit');
   ```

4. **Handle Null Values Gracefully**
   ```php
   // Good - helper handles nulls
   DatatableHelper::createTooltipLink($record->url, $record->name);
   
   // The helper will return empty string for null values
