# DzHelper Documentation

## Overview
The `DzHelper` class provides utilities for extracting information about the current route, controller, and action in Laravel applications.

## Purpose
- Extract current controller name from route
- Get current action method name
- Provide route introspection utilities for dynamic behavior

## Key Features
- ✅ **Route Introspection** - Get current route information
- ✅ **Controller Detection** - Extract controller class name
- ✅ **Action Detection** - Get current method being executed
- ✅ **Lightweight** - Simple utility methods with no dependencies

## Methods

### `action(): string`
Gets the current action method name from the active route.

**Returns:**
- `string` - Current action method name

**Usage:**
```php
// In a controller method named 'index'
$currentAction = DzHelper::action();
// Output: "index"

// In a controller method named 'store'  
$currentAction = DzHelper::action();
// Output: "store"

// Use in conditional logic
if (DzHelper::action() === 'create') {
    // Special handling for create action
    $pageTitle = 'Create New Record';
}
```

### `controller(): string`
Gets the current controller class name from the active route.

**Returns:**
- `string` - Current controller class name (without namespace)

**Usage:**
```php
// In UserController
$currentController = DzHelper::controller();
// Output: "UserController"

// In ProductController
$currentController = DzHelper::controller();
// Output: "ProductController"

// Use for dynamic behavior
$controllerName = DzHelper::controller();
if ($controllerName === 'AdminController') {
    // Apply admin-specific logic
    $this->middleware('admin');
}
```

## Use Cases

### 1. Dynamic Page Titles
```php
// In a base controller or service
public function getPageTitle()
{
    $controller = DzHelper::controller();
    $action = DzHelper::action();
    
    $titles = [
        'UserController' => [
            'index' => 'All Users',
            'create' => 'Create New User',
            'edit' => 'Edit User',
            'show' => 'User Details'
        ],
        'ProductController' => [
            'index' => 'Products List',
            'create' => 'Add Product',
            'edit' => 'Edit Product'
        ]
    ];
    
    return $titles[$controller][$action] ?? ucfirst($action);
}
```

### 2. Conditional CSS Classes
```php
// In a view service provider or helper
public function getBodyClasses()
{
    $controller = str_replace('Controller', '', DzHelper::controller());
    $action = DzHelper::action();
    
    return [
        'controller-' . strtolower($controller),
        'action-' . $action,
        'page-' . strtolower($controller) . '-' . $action
    ];
}

// In Blade layout:
// <body class="{{ implode(' ', app('viewHelper')->getBodyClasses()) }}">
```

### 3. Breadcrumb Generation
```php
public function generateBreadcrumbs()
{
    $controller = str_replace('Controller', '', DzHelper::controller());
    $action = DzHelper::action();
    
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('dashboard')]
    ];
    
    // Add controller-level breadcrumb
    if ($controller !== 'Dashboard') {
        $breadcrumbs[] = [
            'title' => ucfirst($controller),
            'url' => route(strtolower($controller) . '.index')
        ];
    }
    
    // Add action-level breadcrumb
    if (!in_array($action, ['index'])) {
        $breadcrumbs[] = [
            'title' => ucfirst($action),
            'url' => null // Current page
        ];
    }
    
    return $breadcrumbs;
}
```

### 4. Permission-Based Logic
```php
public function checkActionPermission()
{
    $controller = DzHelper::controller();
    $action = DzHelper::action();
    
    $permissionMap = [
        'UserController' => [
            'index' => 'users.view',
            'create' => 'users.create',
            'store' => 'users.create',
            'edit' => 'users.edit',
            'update' => 'users.edit',
            'destroy' => 'users.delete'
        ]
    ];
    
    $permission = $permissionMap[$controller][$action] ?? null;
    
    if ($permission && !auth()->user()->can($permission)) {
        abort(403, 'Access denied');
    }
}
```

### 5. Analytics Tracking
```php
public function trackPageView()
{
    $controller = DzHelper::controller();
    $action = DzHelper::action();
    
    $pageData = [
        'controller' => $controller,
        'action' => $action,
        'page' => $controller . '@' . $action,
        'timestamp' => now(),
        'user_id' => auth()->id()
    ];
    
    // Send to analytics service
    Analytics::track('page_view', $pageData);
}
```

### 6. Dynamic Menu Active States
```php
public function getActiveMenuClass($menuController, $menuActions = [])
{
    $currentController = DzHelper::controller();
    $currentAction = DzHelper::action();
    
    if ($currentController === $menuController) {
        if (empty($menuActions) || in_array($currentAction, $menuActions)) {
            return 'active';
        }
    }
    
    return '';
}

// In Blade template:
// <li class="{{ getActiveMenuClass('UserController', ['index', 'show']) }}">
//     <a href="{{ route('users.index') }}">Users</a>
// </li>
```

## Advanced Usage

### Middleware Integration
```php
// Custom middleware
public function handle($request, Closure $next)
{
    $controller = DzHelper::controller();
    $action = DzHelper::action();
    
    // Log all admin controller access
    if (str_contains($controller, 'Admin')) {
        Log::info('Admin action accessed', [
            'controller' => $controller,
            'action' => $action,
            'user' => auth()->user()->email ?? 'guest'
        ]);
    }
    
    return $next($request);
}
```

### Service Provider Usage
```php
// In a service provider
public function boot()
{
    // Share controller and action with all views
    View::composer('*', function ($view) {
        $view->with([
            'currentController' => DzHelper::controller(),
            'currentAction' => DzHelper::action()
        ]);
    });
}
```

### Route Model Binding Context
```php
public function resolveRouteBinding($value, $field = null)
{
    $action = DzHelper::action();
    
    // Different query behavior based on action
    switch ($action) {
        case 'edit':
        case 'update':
            return $this->where('id', $value)->where('editable', true)->firstOrFail();
        
        case 'show':
            return $this->where('id', $value)->with('relationships')->firstOrFail();
        
        default:
            return $this->where('id', $value)->firstOrFail();
    }
}
```

## Best Practices

1. **Use for Dynamic Behavior Only**
   ```php
   // Good - dynamic behavior based on context
   $pageTitle = $this->generateTitleFromRoute();
   
   // Avoid - hardcode when route is known
   if (DzHelper::action() === 'index') {
       $title = 'Users List'; // Could be hardcoded in specific controller
   }
   ```

2. **Cache Results in Long-Running Operations**
   ```php
   public function processRequest()
   {
       $controller = DzHelper::controller();
       $action = DzHelper::action();
       
       // Cache for multiple uses in same request
       $routeContext = compact('controller', 'action');
       
       return $this->handleWithContext($routeContext);
   }
   ```

3. **Combine with Route Names for Better Context**
   ```php
   public function getRouteContext()
   {
       return [
           'name' => Route::currentRouteName(),
           'controller' => DzHelper::controller(),
           'action' => DzHelper::action()
       ];
   }
   ```

## Limitations

- **Route-Dependent**: Only works when called within route context
- **String Parsing**: Relies on Laravel's route action string format
- **No Parameters**: Doesn't extract route parameters or method arguments

## Alternative Approaches

For more comprehensive route information:

```php
// Laravel's built-in route methods
$route = Route::current();
$name = Route::currentRouteName();
$action = Route::currentRouteAction();

// Request-based approach
$request = request();
$route = $request->route();
$controller = $route->getController();
$method = $route->getActionMethod();
```

## Related Classes
- Laravel's Route facade - DzHelper extracts information from current route
- Middleware classes - Often used together for request context
- View composers - Commonly used to share route context with views
