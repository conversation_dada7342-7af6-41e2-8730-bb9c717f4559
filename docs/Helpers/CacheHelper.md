# CacheHelper Documentation

## Overview
The `CacheHelper` class provides specialized caching utilities for database schema information and module configurations, optimizing performance by reducing repeated database queries and expensive operations.

## Purpose
- Cache database schema information (table columns, structure)
- Cache module configuration data to avoid repeated processing
- Provide centralized cache management with consistent key generation
- Support both time-based (TTL) and permanent caching strategies

## Key Features
- ✅ **Schema Caching** - Database table structure and column information
- ✅ **Module Caching** - CRM module configurations and column definitions
- ✅ **Flexible TTL** - Support for both temporary and permanent caching
- ✅ **Key Management** - Uses Cache<PERSON>ey helper for consistent cache key generation
- ✅ **Closure Support** - Lazy evaluation of expensive operations

## Methods

### Core Caching Method

#### `getCachedKeyData(string $cacheKey, Closure $data, $ttl = null)`
Generic method for caching any data with optional TTL.

**Parameters:**
- `$cacheKey` (string) - Unique cache key identifier
- `$data` (Closure) - Closure that returns the data to cache
- `$ttl` (mixed) - Time to live (null for permanent cache)

**Returns:**
- `mixed` - Cached data or freshly computed data

**Usage:**
```php
// Cache with TTL
$result = CacheHelper::getCachedKeyData(
    'expensive_calculation_123',
    function() {
        return performExpensiveCalculation();
    },
    now()->addHours(2)
);

// Cache permanently
$result = CacheHelper::getCachedKeyData(
    'permanent_config',
    function() {
        return loadConfiguration();
    }
);
```

### Schema Caching Methods

#### `getCachedSchemaTableColumnsListing(string $tableName)`
Caches the list of column names for a database table.

**Usage:**
```php
// Get cached column names
$columns = CacheHelper::getCachedSchemaTableColumnsListing('users');
// Returns: ['id', 'name', 'email', 'created_at', 'updated_at']

// First call queries database, subsequent calls use cache
$productColumns = CacheHelper::getCachedSchemaTableColumnsListing('products');
```

#### `getCachedSchemaTableColumns(string $tableName)`
Caches detailed column information for a database table.

**Usage:**
```php
// Get detailed column information
$columnDetails = CacheHelper::getCachedSchemaTableColumns('users');
// Returns: Array with column types, constraints, defaults, etc.

$tableSchema = CacheHelper::getCachedSchemaTableColumns('orders');
// Contains full column definitions with metadata
```

### Module Caching Methods

#### `getCachedAvailableModuleColumns(string $module)`
Caches available columns configuration for CRM modules.

**Usage:**
```php
// Cache module column configuration
$userColumns = CacheHelper::getCachedAvailableModuleColumns('users');
$productColumns = CacheHelper::getCachedAvailableModuleColumns('products');

// Returns processed column definitions for datatables
foreach ($userColumns as $column) {
    echo "Column: " . $column['name'] . " Type: " . $column['show_in_index_type'];
}
```

## Use Cases

### 1. Dynamic Form Generation
```php
public function generateForm($tableName)
{
    // Cache expensive schema lookup
    $columns = CacheHelper::getCachedSchemaTableColumnsListing($tableName);
    
    $formFields = [];
    foreach ($columns as $column) {
        if (!in_array($column, ['id', 'created_at', 'updated_at'])) {
            $formFields[] = $this->createFormField($column);
        }
    }
    
    return view('dynamic-form', compact('formFields'));
}
```

### 2. Datatable Column Configuration
```php
public function getDatatableColumns($module)
{
    // Cache module configuration to avoid repeated processing
    $availableColumns = CacheHelper::getCachedAvailableModuleColumns($module);
    
    return collect($availableColumns)
        ->where('show_in_index', true)
        ->values()
        ->toArray();
}
```

### 3. Database Migration Validation
```php
public function validateTableStructure($tableName, $expectedColumns)
{
    $actualColumns = CacheHelper::getCachedSchemaTableColumnsListing($tableName);
    
    $missing = array_diff($expectedColumns, $actualColumns);
    $extra = array_diff($actualColumns, $expectedColumns);
    
    return [
        'valid' => empty($missing) && empty($extra),
        'missing' => $missing,
        'extra' => $extra
    ];
}
```

### 4. API Schema Documentation
```php
public function generateApiSchema($tableName)
{
    $columns = CacheHelper::getCachedSchemaTableColumns($tableName);
    
    $schema = [];
    foreach ($columns as $column) {
        $schema[$column['name']] = [
            'type' => $this->mapDbTypeToJsonType($column['type']),
            'required' => !$column['nullable'],
            'default' => $column['default']
        ];
    }
    
    return response()->json($schema);
}
```

### 5. Custom Query Builder
```php
public function buildSelectQuery($table, $selectedFields = [])
{
    $availableColumns = CacheHelper::getCachedSchemaTableColumnsListing($table);
    
    // Validate selected fields against available columns
    $validFields = array_intersect($selectedFields, $availableColumns);
    
    if (empty($validFields)) {
        $validFields = $availableColumns;
    }
    
    return DB::table($table)->select($validFields);
}
```

## Advanced Usage

### Custom Cache TTL Strategies
```php
public function getCachedUserStats($userId)
{
    $cacheKey = "user_stats_{$userId}";
    
    return CacheHelper::getCachedKeyData(
        $cacheKey,
        function() use ($userId) {
            return $this->calculateExpensiveUserStats($userId);
        },
        now()->addMinutes(15) // Refresh every 15 minutes
    );
}
```

### Conditional Caching
```php
public function getCachedModuleData($module, $forceRefresh = false)
{
    $cacheKey = CacheKey::availableModuleColumns($module);
    
    if ($forceRefresh) {
        Cache::forget($cacheKey);
    }
    
    return CacheHelper::getCachedKeyData($cacheKey, function() use ($module) {
        return $this->loadModuleConfiguration($module);
    });
}
```

### Bulk Cache Operations
```php
public function preloadSchemaCache($tables)
{
    foreach ($tables as $table) {
        // Pre-warm cache for multiple tables
        CacheHelper::getCachedSchemaTableColumnsListing($table);
        CacheHelper::getCachedSchemaTableColumns($table);
    }
}
```

## Cache Management

### Cache Invalidation
```php
// Clear specific schema cache
public function clearTableSchemaCache($tableName)
{
    Cache::forget(CacheKey::schemaTableColumnsListing($tableName));
    Cache::forget(CacheKey::schemaTableColumns($tableName));
}

// Clear module cache
public function clearModuleCache($module)
{
    Cache::forget(CacheKey::availableModuleColumns($module));
}

// Clear all schema caches
public function clearAllSchemaCache()
{
    Cache::flush(); // Nuclear option - clears all cache
    
    // Or more targeted approach:
    $tables = DB::select('SHOW TABLES');
    foreach ($tables as $table) {
        $this->clearTableSchemaCache($table->Tables_in_database);
    }
}
```

### Cache Monitoring
```php
public function getCacheStats()
{
    return [
        'schema_tables_cached' => $this->countSchemaCacheEntries(),
        'module_configs_cached' => $this->countModuleCacheEntries(),
        'total_cache_size' => $this->estimateCacheSize()
    ];
}

private function countSchemaCacheEntries()
{
    $count = 0;
    $tables = DB::select('SHOW TABLES');
    
    foreach ($tables as $table) {
        $tableName = $table->Tables_in_database;
        if (Cache::has(CacheKey::schemaTableColumnsListing($tableName))) {
            $count++;
        }
    }
    
    return $count;
}
```

## Best Practices

1. **Use Appropriate TTL**
   ```php
   // Schema rarely changes - cache permanently
   CacheHelper::getCachedSchemaTableColumns($table); // Permanent
   
   // User stats change frequently - use TTL
   CacheHelper::getCachedKeyData($key, $closure, now()->addMinutes(30));
   ```

2. **Handle Cache Misses Gracefully**
   ```php
   try {
       $data = CacheHelper::getCachedKeyData($key, function() {
           return expensiveOperation();
       });
   } catch (Exception $e) {
       Log::warning('Cache operation failed', ['key' => $key, 'error' => $e->getMessage()]);
       return fallbackData();
   }
   ```

3. **Use Descriptive Cache Keys**
   ```php
   // Good - uses CacheKey helper for consistency
   $key = CacheKey::schemaTableColumns($tableName);
   
   // Avoid - inconsistent naming
   $key = "table_cols_" . $tableName;
   ```

4. **Monitor Cache Performance**
   ```php
   // Log cache hits/misses for optimization
   public function getCachedDataWithMetrics($key, $closure, $ttl = null)
   {
       $startTime = microtime(true);
       $hit = Cache::has($key);
       
       $result = CacheHelper::getCachedKeyData($key, $closure, $ttl);
       
       $duration = microtime(true) - $startTime;
       Log::info('Cache operation', [
           'key' => $key,
           'hit' => $hit,
           'duration_ms' => $duration * 1000
       ]);
       
       return $result;
   }
   ```

## Integration with Other Systems

### With ModuleHelper
```php
// CacheHelper works closely with ModuleHelper
$moduleColumns = CacheHelper::getCachedAvailableModuleColumns('users');
// Uses ModuleHelper::getRelatedModuleName() and ModuleHelper::getCrmCoreModule()
```

### With Database Migrations
```php
// Clear cache after migrations
public function up()
{
    Schema::create('new_table', function (Blueprint $table) {
        $table->id();
        $table->string('name');
        $table->timestamps();
    });
    
    // Clear schema cache to pick up new table
    Cache::forget(CacheKey::schemaTableColumnsListing('new_table'));
}
```

## Performance Benefits

- **Database Query Reduction**: Schema queries cached indefinitely
- **Module Processing**: Expensive module configuration processing cached
- **Memory Efficiency**: Lazy loading with closures
- **Response Time**: Significant reduction in page load times for schema-heavy operations

## Related Classes
- [CacheKey](CacheKey.md) - Provides consistent cache key generation
- [ModuleHelper](ModuleHelper.md) - Used for module configuration loading
- [Schema](https://laravel.com/docs/migrations#examining-tables) - Laravel's Schema facade for database introspection
