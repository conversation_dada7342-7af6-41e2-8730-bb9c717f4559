<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Modules\Offer\Models\Offer;

trait HasOffers
{
    use HasRelationships;

    private $offerClass = null;

    public static function bootHasOffers()
    {
        static::deleting(function ($model) {
            if (method_exists($model, 'isForceDeleting') && !$model->isForceDeleting()) {
                return;
            }
            $model->offers()->detach();
        });
    }

    /**
     * A model may have multiple offers.
     */
    public function offers(): BelongsToMany
    {
        return $this->morphToMany(
            Offer::class,
            'model',
            'model_has_offers',
            'model_id',
        )->withPivot('qty', 'price');
    }

    /**
     * Revoke the given role from the model.
     *
     * @param  string|int|Offer|\BackedEnum  $offer
     */
    public function removeOffer($offer): HasOffers
    {
        $this->offers()->detach($this->getStoredOffer($offer));

        $this->unsetRelation('offers');

        return $this;
    }

    /**
     * @param $offer
     * @return Offer
     */
    protected function getStoredOffer($offer): Offer
    {
        if (!$this->getOfferClass()::find($offer)) {

            dd($this->getOfferClass()::find($offer), $offer);
        }
        if (is_int($offer)) {
            return $this->getOfferClass()::find($offer);
        }
        dd($offer);
        return $offer;
    }

    public function getOfferClass(): string
    {
        if (!$this->offerClass) {
            $this->offerClass = Offer::class;
        }

        return $this->offerClass;
    }

    /**
     * Remove all current offers and set the given ones.
     *
     * @param  string|int|array|Offer|Collection|\BackedEnum  ...$offers
     * @return $this
     */
    public function syncOffers(...$offers)
    {
        if ($this->getModel()->exists) {
            $this->collectOffers($offers);
            $this->offers()->detach();
            $this->setRelation('offers', collect());
        }

        return $this->assignOffer($offers);
    }

    /**
     * Returns array of role ids
     *
     * @param  string|int|array|Offer|Collection|\BackedEnum  $offers
     */
    private function collectOffers(...$offers): array
    {
        return collect($offers)
            ->flatten()
            ->reduce(function ($array, $offer) {
                if (empty($offer)) {
                    return $array;
                }

                $offer = $this->getStoredOffer($offer);
                if (!$offer instanceof Offer) {
                    return $array;
                }

                if (!in_array($offer->getKey(), $array)) {
                    $array[] = $offer->getKey();
                }

                return $array;
            }, []);
    }

    /**
     * Assign the given role to the model.
     *
     * @param $offer
     * @param $qty
     * @param $price
     * @return $this
     */
    public function assignOffer($offer, $qty, $price)
    {
        $offers = $this->collectOffers($offer);

        $model = $this->getModel();

        if ($model->exists) {
            $currentOffers = $this->offers->map(fn($offer) => $offer->getKey())->toArray();
            $this->offers()->attach(array_diff($offers, $currentOffers), ['qty' => $qty, 'price' => $price, 'created_at' => now()]);
            $model->unsetRelation('offers');
        } else {
            $class = \get_class($model);
            $saved = false;

            $class::saved(
                function ($object) use ($offers, $model, &$saved, $qty, $price) {
                    if ($saved || $model->getKey() != $object->getKey()) {
                        return;
                    }
                    $model->offers()->attach($offers, ['qty' => $qty, 'price' => $price]);
                    $model->unsetRelation('offers');
                    $saved = true;
                }
            );
        }

        return $this;
    }

    /**
     * Determine if the model has any of the given role(s).
     *
     * Alias to hasOffer() but without Guard controls
     *
     * @param  string|int|array|Offer|Collection|\BackedEnum  $offers
     */
    public function hasAnyOffer(...$offers): bool
    {
        return $this->hasOffer($offers);
    }

    /**
     * Determine if the model has (one of) the given role(s).
     *
     * @param  string|int|array|Offer|Collection|\BackedEnum  $offers
     */
    public function hasOffer($offers): bool
    {
        $this->loadMissing('offers');

        if (is_string($offers) && strpos($offers, '|') !== false) {
            $offers = $this->convertPipeToArray($offers);
        }

        if ($offers instanceof \BackedEnum) {
            $offers = $offers->value;

            return $this->offers
                ->pluck('name')
                ->contains(function ($name) use ($offers) {
                    /** @var string|\BackedEnum $name */
                    if ($name instanceof \BackedEnum) {
                        return $name->value == $offers;
                    }

                    return $name == $offers;
                });
        }

        if (is_int($offers)) {
            $key = (new ($this->getOfferClass())())->getKeyName();

            return $this->offers->contains($key, $offers);
        }

        if (is_string($offers)) {
            return $this->offers->contains('name', $offers);
        }

        if ($offers instanceof Offer) {
            return $this->offers->contains($offers->getKeyName(), $offers->getKey());
        }

        if (is_array($offers)) {
            foreach ($offers as $offer) {
                if ($this->hasOffer($offer)) {
                    return true;
                }
            }

            return false;
        }

        if ($offers instanceof Collection) {
            return $offers->intersect($this->offers)->isNotEmpty();
        }

        throw new \TypeError('Unsupported type for $offers parameter to hasOffer().');
    }

    public function getOfferNames(): Collection
    {
        $this->loadMissing('offers');

        return $this->offers->pluck('name');
    }
}
