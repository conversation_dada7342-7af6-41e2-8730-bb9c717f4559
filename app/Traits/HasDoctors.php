<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Modules\Doctor\Models\Doctor;

trait HasDoctors
{
    use HasRelationships;

    private $doctorClass = null;

    public static function bootHasDoctors()
    {
        static::deleting(function ($model) {
            if (method_exists($model, 'isForceDeleting') && !$model->isForceDeleting()) {
                return;
            }
            $model->doctors()->detach();
        });
    }

    /**
     * A model may have multiple doctors.
     */
    public function doctors(): BelongsToMany
    {
        return $this->morphToMany(
            Doctor::class,
            'model',
            'model_has_doctors',
            'model_id',
        );
    }

    /**
     * Revoke the given role from the model.
     *
     * @param  string|int|Doctor|\BackedEnum  $doctor
     */
    public function removeDoctor($doctor): HasDoctors
    {
        $this->doctors()->detach($this->getStoredDoctor($doctor));

        $this->unsetRelation('doctors');

        return $this;
    }

    /**
     * @param $doctor
     * @return Doctor
     */
    protected function getStoredDoctor($doctor): Doctor
    {
        if (!$this->getDoctorClass()::find($doctor)) {

            dd($this->getDoctorClass()::find($doctor), $doctor);
        }
        if (is_int($doctor)) {
            return $this->getDoctorClass()::find($doctor);
        }
        dd($doctor);
        return $doctor;
    }

    public function getDoctorClass(): string
    {
        if (!$this->serviceClass) {
            $this->serviceClass = Doctor::class;
        }

        return $this->serviceClass;
    }

    /**
     * Remove all current doctors and set the given ones.
     *
     * @param  string|int|array|Doctor|Collection|\BackedEnum  ...$doctors
     * @return $this
     */
    public function syncDoctors(...$doctors)
    {
        if ($this->getModel()->exists) {
            $this->collectDoctors($doctors);
            $this->doctors()->detach();
            $this->setRelation('doctors', collect());
        }

        return $this->assignDoctor($doctors);
    }

    /**
     * Returns array of role ids
     *
     * @param  string|int|array|Doctor|Collection|\BackedEnum  $doctors
     */
    private function collectDoctors(...$doctors): array
    {
        return collect($doctors)
            ->flatten()
            ->reduce(function ($array, $doctor) {
                if (empty($doctor)) {
                    return $array;
                }

                $doctor = $this->getStoredDoctor($doctor);
                if (!$doctor instanceof Doctor) {
                    return $array;
                }

                if (!in_array($doctor->getKey(), $array)) {
                    $array[] = $doctor->getKey();
                }

                return $array;
            }, []);
    }

    /**
     * Assign the given role to the model.
     *
     * @param $doctor
     * @return $this
     */
    public function assignDoctor($doctor)
    {
        $doctors = $this->collectDoctors($doctor);

        $model = $this->getModel();

        if ($model->exists) {
            $currentDoctors = $this->doctors->map(fn($doctor) => $doctor->getKey())->toArray();
            $this->doctors()->attach(array_diff($doctors, $currentDoctors), ['created_at' => now()]);
            $model->unsetRelation('doctors');
        } else {
            $class = \get_class($model);
            $saved = false;

            $class::saved(
                function ($object) use ($doctors, $model, &$saved) {
                    if ($saved || $model->getKey() != $object->getKey()) {
                        return;
                    }
                    $model->doctors()->attach($doctors);
                    $model->unsetRelation('doctors');
                    $saved = true;
                }
            );
        }

        return $this;
    }

    /**
     * Determine if the model has any of the given role(s).
     *
     * Alias to hasDoctor() but without Guard controls
     *
     * @param  string|int|array|Doctor|Collection|\BackedEnum  $doctors
     */
    public function hasAnyDoctor(...$doctors): bool
    {
        return $this->hasDoctor($doctors);
    }

    /**
     * Determine if the model has (one of) the given role(s).
     *
     * @param  string|int|array|Doctor|Collection|\BackedEnum  $doctors
     */
    public function hasDoctor($doctors): bool
    {
        $this->loadMissing('doctors');

        if (is_string($doctors) && strpos($doctors, '|') !== false) {
            $doctors = $this->convertPipeToArray($doctors);
        }

        if ($doctors instanceof \BackedEnum) {
            $doctors = $doctors->value;

            return $this->doctors
                ->pluck('name')
                ->contains(function ($name) use ($doctors) {
                    /** @var string|\BackedEnum $name */
                    if ($name instanceof \BackedEnum) {
                        return $name->value == $doctors;
                    }

                    return $name == $doctors;
                });
        }

        if (is_int($doctors)) {
            $key = (new ($this->getDoctorClass())())->getKeyName();

            return $this->doctors->contains($key, $doctors);
        }

        if (is_string($doctors)) {
            return $this->doctors->contains('name', $doctors);
        }

        if ($doctors instanceof Doctor) {
            return $this->doctors->contains($doctors->getKeyName(), $doctors->getKey());
        }

        if (is_array($doctors)) {
            foreach ($doctors as $doctor) {
                if ($this->hasDoctor($doctor)) {
                    return true;
                }
            }

            return false;
        }

        if ($doctors instanceof Collection) {
            return $doctors->intersect($this->doctors)->isNotEmpty();
        }

        throw new \TypeError('Unsupported type for $doctors parameter to hasDoctor().');
    }

    /**
     * @return Collection
     */
    public function getDoctorNames(): Collection
    {
        $this->loadMissing('doctors');

        return $this->doctors->pluck('name');
    }
}
