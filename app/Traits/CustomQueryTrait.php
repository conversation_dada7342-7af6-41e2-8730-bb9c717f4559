<?php

namespace App\Traits;


trait CustomQueryTrait
{
    /**
     * @param $filter
     * @param  null  $relation
     * @param  null  $queryColumn
     * @param  bool  $withRelationName
     * @return mixed|string
     */
    protected function getQueryColumnName($filter, $relation = null, $queryColumn = null, $withRelationName = true)
    {
        $query_column = $queryColumn ?? $filter['column'];

        if ($relation and $withRelationName) {
            // properties:options:slug:{SLUG}:{ID}
            $query_column = $filter['relation_key'] ?? $query_column;
            $query_column = "{$relation}.{$query_column}";
        }

        return $query_column;
    }

    /**
     * @param $filter
     *
     * @return array
     */
    protected function getFilterRange($filter)
    {
        $query_1 = $filter['query_1'];
        $query_2 = $filter['query_2'];

        return $query_1 < $query_2 ? [$query_1, $query_2] : [$query_2, $query_1];
    }

    /**
     * @param $columnName
     * @param $query
     *
     * @return string
     *
     * This function returns the relation name specified in the column properties of a model
     * based on a column's type.
     */
    protected function getColumnRelation($columnName, $query)
    {
        $model = $query->getModel();
        $modelColumns = new $model::$columns;
        $targetColumn = $modelColumns->columns->where('name', $columnName)->count() == 0 ? null : $modelColumns->columns->where('name', $columnName)->first();
        if ($targetColumn != null && isset($targetColumn['relation_name'])) {
            return $targetColumn['relation_name'];
        }

        return null;
    }
}
