<?php


namespace App\Traits;


use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

trait CrmCommandTrait
{
    /**
     * @param $message
     */
    function commandStartInfo($message): void
    {
        $this->line("\n$ {$message}\n --------------------------------------------------");
    }


    /**
     * @param $message
     * @param  bool  $withLine
     */
    function warnEnd($message, $withLine = true): void
    {
        $message = "<comment>✘ $message</comment>";
        if ($withLine) {
            $message .= "\n--------------------------------------------------\n";
        }
        $this->line($message);
    }


    /**
     * @param $message
     */
    function commandEndInfo($message): void
    {
        $this->line("<info>✔ $message</info> \n==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==\n");
    }

    /**
     * @param $clear_notifications_time
     * @return \Carbon\Carbon
     */
    function getClearTimeFromConfig($clear_notifications_time)
    {
        $now = \Carbon\Carbon::now();

        $clear_notifications_time = trim(Str::lower($clear_notifications_time));

        switch ($clear_notifications_time) {
            case 'daily':
                $clear_time = $now->subDay();
                break;
            case 'weekly':
                $clear_time = $now->subWeek();
                break;
            case 'yearly':
                $clear_time = $now->subYear();
                break;
            case 'monthly':
            default:
                $clear_time = $now->subMonth();
                break;
        }
        return $clear_time;
    }

    /**
     * @param $row
     * @param $column_names
     * @param  null  $event_class
     * @param  null  $event_function
     * @param  null  $original
     * @param  null  $old_values
     * @param  null  $new_values
     * @param  string  $event_type
     * @param  bool  $bySystem
     */
    function save_audit(
        $row,
        $column_names,
        $event_class = null,
        $event_function = null,
        $original = null,
        $old_values = null,
        $new_values = null,
        string $event_type = 'updated',
        bool $bySystem = true
    ) {
        $event_function = $event_function ? Str::snake($event_function) : $event_function;
        $audit_changes = [
            'event'          => $event_type,
            'auditable_type' => get_class($row),
            'auditable_id'   => $row->id,
            'url'            => request()->fullUrl(),
            'event_class'    => $event_class,
            'event_function' => $event_function,
            'ip_address'     => request()->ip(),
            'user_agent'     => request()->header('User-Agent'),
            // 'tags'           => '',
        ];

        if (auth()->check()) {
            $user_id = auth()->id();
        }

        $created_by = $user_id ?? ($row->modified_by ?? $row->created_by);

        if ($bySystem) {
            $user_type = null;
            $user_id = null;
        } else {
            $user_type = User::class;
            $user_id = $created_by;
        }

        $audit_changes['user_type'] = $user_type;
        $audit_changes['user_id'] = $user_id;
        $audit_changes['created_by'] = $created_by;

        $original = $original ?? $row->getOriginal();

        if (is_null($old_values) and is_null($new_values)) {
            $old_values = [];
            $new_values = [];
            $column_names = is_array($column_names) ? $column_names : [$column_names];
            foreach ($column_names as $column_name) {
                $old_values[] = $original[$column_name] ?? null;
                $new_values[] = $row->{$column_name};
            }
        }

        $this->create_new_audit($column_names, $old_values, $new_values, $audit_changes);
    }

    /**
     * @param $column_names
     * @param $old_values
     * @param $new_values
     * @param $audit_changes
     * @return int|void
     */
    function create_new_audit($column_names, $old_values, $new_values, $audit_changes)
    {
        if (is_null($old_values) and is_null($new_values)) {
            return 0;
        }

        $old_values = is_array($old_values) ? $old_values : [$old_values];
        $new_values = is_array($new_values) ? $new_values : [$new_values];
        $column_names = is_array($column_names) ? $column_names : [$column_names];

        $old_values_data = [];
        $new_values_data = [];

        foreach ($column_names as $key => $column_name) {
            $old_value = $old_values[$key] ?? null;
            $new_value = $new_values[$key] ?? null;
            if ($old_value != $new_value) {
                $old_values_data[$column_name] = $old_value;
                $new_values_data[$column_name] = $new_value;
            }
        }

        if (count($old_values_data) or count($new_values_data)) {
            $audit_changes['old_values'] = $old_values_data;
            $audit_changes['new_values'] = $new_values_data;

            save_db_audit($audit_changes);
        }
    }

    /**
     * monoSpace
     *
     * @param  mixed  $text
     * @param  mixed  $length
     * @return void
     */
    public function monoSpace($text, $length = 25)
    {
        $text = trim($text);

        $spaceCount = $length - strlen($text);

        if ($spaceCount <= 0) {
            return $text;
        }

        for ($i = 0; $i < $spaceCount; $i++) {

            if ($i % 2 == 0) {
                $text = " $text";
            } else {
                $text = "$text ";
            }
        }

        return $text;
    }

    /**
     * @param $row
     * @param  string  $column_name
     * @param  int  $i
     * @return null
     */
    public function getSubValue($row, string $column_name, int $i)
    {
        if (isset($row->{$column_name}[$i])) {
            $value = $row->{$column_name}[$i];
            if (!is_null($value) and !empty($value) and ($value != 0)) {
                return $value;
            }
        }
        return null;
    }

    /**
     * @param  string  $logMessage
     * @param  string  $channel
     */
    private function save_and_print_info_log(string $channel, string $logMessage)
    {
        $this->save_and_print_log($channel, $logMessage, 'info');
    }

    /**
     * @param  string  $logMessage
     * @param  string  $log_type
     * @param  string  $channel
     */
    private function save_and_print_log(string $channel, string $logMessage, $log_type = 'warning')
    {
        switch ($log_type) {
            case 'debug':
            case 'notice':
            case 'alert':
            case 'warning':
                $type = 'warn';
                break;
            case 'emergency':
            case 'error':
                $type = 'error';
                break;
            case 'info':
            default:
                $type = $log_type;
                break;
        }

        Log::channel($channel)->{$log_type}($logMessage);

        $this->{$type}($logMessage);
    }

    /**
     * @param  string  $channel
     * @param  string  $logMessage
     */
    private function save_and_print_warn_log(string $channel, string $logMessage)
    {
        $this->save_and_print_log($channel, $logMessage, 'warning');
    }

    /**
     * @param $row
     * @param  array  $columns
     * @return mixed
     */
    private function get_max_count($row, array $columns)
    {
        $countArray = [];
        foreach ($columns as $column) {
            $countArray[] = count($row->{$column} ?? []);
        }

        return max($countArray);
    }
}
