<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Modules\Cases\Models\MedicalCase;

trait HasMedicalCases
{
    use HasRelationships;

    private $medicalCaseClass = null;

    public static function bootHasMedicalCases()
    {
        static::deleting(function ($model) {
            if (method_exists($model, 'isForceDeleting') && !$model->isForceDeleting()) {
                return;
            }
            $model->medical_cases()->detach();
        });
    }

    /**
     * A model may have multiple medical_cases.
     */
    public function medical_cases(): BelongsToMany
    {
        return $this->morphToMany(
            MedicalCase::class,
            'model',
            'model_has_medical_cases',
            'model_id',
        );
    }

    /**
     * Revoke the given role from the model.
     *
     * @param  string|int|MedicalCase|\BackedEnum  $medicalCase
     */
    public function removeMedicalCase($medicalCase): HasMedicalCases
    {
        $this->medical_cases()->detach($this->getStoredMedicalCase($medicalCase));

        $this->unsetRelation('medical_cases');

        return $this;
    }

    /**
     * @param $medicalCase
     * @return MedicalCase
     */
    protected function getStoredMedicalCase($medicalCase): MedicalCase
    {
        if (!$this->getMedicalCaseClass()::find($medicalCase)) {

            dd($this->getMedicalCaseClass()::find($medicalCase), $medicalCase);
        }
        if (is_int($medicalCase)) {
            return $this->getMedicalCaseClass()::find($medicalCase);
        }
        dd($medicalCase);
        return $medicalCase;
    }

    public function getMedicalCaseClass(): string
    {
        if (!$this->serviceClass) {
            $this->serviceClass = MedicalCase::class;
        }

        return $this->serviceClass;
    }

    /**
     * Remove all current medical_cases and set the given ones.
     *
     * @param  string|int|array|MedicalCase|Collection|\BackedEnum  ...$medicalCases
     * @return $this
     */
    public function syncMedicalCases(...$medicalCases)
    {
        if ($this->getModel()->exists) {
            $this->collectMedicalCases($medicalCases);
            $this->medical_cases()->detach();
            $this->setRelation('medical_cases', collect());
        }

        return $this->assignMedicalCase($medicalCases);
    }

    /**
     * Returns array of role ids
     *
     * @param  string|int|array|MedicalCase|Collection|\BackedEnum  $medicalCases
     */
    private function collectMedicalCases(...$medicalCases): array
    {
        return collect($medicalCases)
            ->flatten()
            ->reduce(function ($array, $medicalCase) {
                if (empty($medicalCase)) {
                    return $array;
                }

                $medicalCase = $this->getStoredMedicalCase($medicalCase);
                if (!$medicalCase instanceof MedicalCase) {
                    return $array;
                }

                if (!in_array($medicalCase->getKey(), $array)) {
                    $array[] = $medicalCase->getKey();
                }

                return $array;
            }, []);
    }

    /**
     * Assign the given role to the model.
     *
     * @param $medicalCase
     * @return $this
     */
    public function assignMedicalCase($medicalCase)
    {
        $medicalCases = $this->collectMedicalCases($medicalCase);

        $model = $this->getModel();

        if ($model->exists) {
            $currentMedicalCases = $this->medical_cases->map(fn($medicalCase) => $medicalCase->getKey())->toArray();
            $this->medical_cases()->attach(array_diff($medicalCases, $currentMedicalCases), ['created_at' => now()]);
            $model->unsetRelation('medical_cases');
        } else {
            $class = \get_class($model);
            $saved = false;

            $class::saved(
                function ($object) use ($medicalCases, $model, &$saved) {
                    if ($saved || $model->getKey() != $object->getKey()) {
                        return;
                    }
                    $model->medical_cases()->attach($medicalCases);
                    $model->unsetRelation('medical_cases');
                    $saved = true;
                }
            );
        }

        return $this;
    }

    /**
     * Determine if the model has any of the given role(s).
     *
     * Alias to hasMedicalCase() but without Guard controls
     *
     * @param  string|int|array|MedicalCase|Collection|\BackedEnum  $medicalCases
     */
    public function hasAnyMedicalCase(...$medicalCases): bool
    {
        return $this->hasMedicalCase($medicalCases);
    }

    /**
     * Determine if the model has (one of) the given role(s).
     *
     * @param  string|int|array|MedicalCase|Collection|\BackedEnum  $medicalCases
     */
    public function hasMedicalCase($medicalCases): bool
    {
        $this->loadMissing('medical_cases');

        if (is_string($medicalCases) && strpos($medicalCases, '|') !== false) {
            $medicalCases = $this->convertPipeToArray($medicalCases);
        }

        if ($medicalCases instanceof \BackedEnum) {
            $medicalCases = $medicalCases->value;

            return $this->medical_cases
                ->pluck('name')
                ->contains(function ($name) use ($medicalCases) {
                    /** @var string|\BackedEnum $name */
                    if ($name instanceof \BackedEnum) {
                        return $name->value == $medicalCases;
                    }

                    return $name == $medicalCases;
                });
        }

        if (is_int($medicalCases)) {
            $key = (new ($this->getMedicalCaseClass())())->getKeyName();

            return $this->medical_cases->contains($key, $medicalCases);
        }

        if (is_string($medicalCases)) {
            return $this->medical_cases->contains('name', $medicalCases);
        }

        if ($medicalCases instanceof MedicalCase) {
            return $this->medical_cases->contains($medicalCases->getKeyName(), $medicalCases->getKey());
        }

        if (is_array($medicalCases)) {
            foreach ($medicalCases as $medicalCase) {
                if ($this->hasMedicalCase($medicalCase)) {
                    return true;
                }
            }

            return false;
        }

        if ($medicalCases instanceof Collection) {
            return $medicalCases->intersect($this->medical_cases)->isNotEmpty();
        }

        throw new \TypeError('Unsupported type for $medicalCases parameter to hasMedicalCase().');
    }

    /**
     * @return Collection
     */
    public function getMedicalCaseNames(): Collection
    {
        $this->loadMissing('medical_cases');

        return $this->medical_cases->pluck('name');
    }
}
