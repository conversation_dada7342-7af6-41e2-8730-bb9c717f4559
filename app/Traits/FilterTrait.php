<?php

namespace App\Traits;

use App\Support\CustomQueryBuilder;
use App\Support\CustomSqlStatements;
use Illuminate\Support\Str;

trait
FilterTrait
{
    /**
     * @param $query
     *
     * @return mixed
     */
    public function scopeAdvancedFilter($query)
    {
        return $this->process($query, request()->all());
    }

    /**
     * @param $query
     * @param $data
     *
     * @return mixed|void
     */
    public function process($query, $data)
    {
        return (new CustomQueryBuilder)->apply($query, $data);
    }

    /**
     * @param      $query
     * @param  null  $filters
     *
     * @return mixed
     */
    public function scopeReportFilter($query, $filters = null)
    {
        $filtersData = [];
        foreach ($filters as $key => $filter) {
            $filter = (object) $filter;
            $filter_value = $filter->value ?? null;
            if (is_array($filter_value)) {
                if (is_object($filter_value[0])) {
                    $filter_value = collect($filter_value)->map(function ($val) {
                        return $val->id;
                    })->toArray();
                }
                $filter_value = implode(',', $filter_value);
            }

            $value2 = $filter->value_2 ?? null;
            $value = $filter_value;
            if (Str::contains($filter->operator, 'between') and Str::contains($filter_value, '-')) {
                $betweenArrayValue = explode('-', $filter_value);
                $value = trim($betweenArrayValue[0]);
                $value2 = trim($betweenArrayValue[1] ?? null);
            }
            $key = $filter->slug ?? $key;
            $filtersData[$key]["check"] = "on";
            $filtersData[$key]["table"] = $filter->table ?? '';
            $filtersData[$key]["column"] = $filter->columnName ?? '';
            $filtersData[$key]["operator"] = $filter->operator ?? '';
            $filtersData[$key]["query_1"] = $value ?? '';
            $filtersData[$key]["query_2"] = $value2 ?? '';
        }
        $data['f'] = $filtersData;

        return $this->process($query, $data);
    }

    /**
     * @param      $query
     * @param  null  $filters
     *
     * @return mixed
     */
    public function scopeFilterSqlStatements($query, $filters = null)
    {
        $report_filters_data = [];
        foreach ($filters as $key => $filter) {
            $filter_value = $filter->value ?? null;
            if (is_array($filter_value)) {
                if (is_object($filter_value[0])) {
                    $filter_value = collect($filter_value)->map(function ($val) {
                        return $val->id;
                    })->toArray();
                }
                $filter_value = implode(',', $filter_value);
            }
            $value = $filter_value;
            $value2 = $filter->value_2 ?? null;
            if (Str::contains($filter->operator, 'between') and Str::contains($filter_value, '-')) {
                $betweenArrayValue = explode('-', $filter_value);
                $value = trim($betweenArrayValue[0]);
                $value2 = trim($betweenArrayValue[1] ?? null);
            }

            $report_filters_data[$key]["table"] = $filter->table ?? '';
            $report_filters_data[$key]["column"] = $filter->columnName ?? '';
            $report_filters_data[$key]["operator"] = $filter->operator ?? '';
            $report_filters_data[$key]["query_1"] = $value ?? '';
            $report_filters_data[$key]["query_2"] = $value2 ?? '';
        }

        return (new CustomSqlStatements)->apply($query, $report_filters_data);
    }

    /**
     * @param $query
     * @param $criteriaPattern
     * @param  null  $statements
     *
     * @return string|string[]
     */
    public function scopeReplaceCriteriaPatternStatements($query, $criteriaPattern, $statements = null)
    {
        $criteriaPattern = Str::lower($criteriaPattern);
        $criteriaPattern = str_replace("or", " or ", $criteriaPattern);
        $criteriaPattern = str_replace("and", " and ", $criteriaPattern);

//        $originalCriteriaPattern = $criteriaPattern;

        // Replace Numbers To _STATEMENT_NUMBER Like _STATEMENT_1
        // Example: 1 AND 2 <=> _STATEMENT_1 AND _STATEMENT_2
        foreach ($statements as $key => $statement) {
            $number = $key + 1;
            $criteriaPattern = str_replace($number, " (_STATEMENT_{$number}) ", $criteriaPattern);
        }

        // Replace _STATEMENT_NUMBER To filter statement
        // Example: _STATEMENT_1 <=> (leads.id < '10')
        foreach ($statements as $key => $statement) {
            $number = $key + 1;
            $criteriaPattern = str_replace("_STATEMENT_{$number}", $statement, $criteriaPattern);
        }

        $criteriaPattern = str_replace("  ", " ", $criteriaPattern);

        return $criteriaPattern;
    }

    /**
     * @return string
     */
    protected function whiteListColumns()
    {
        return implode(',', $this->allowedFilters);
    }

    /**
     * @return string
     */
    protected function orderableColumns()
    {
        return implode(',', $this->orderable);
    }

    /**
     * @return string
     */
    protected function allowedOperators()
    {
        return implode(',', [
            'equal_to',
            'not_equal_to',
            'less_than',
            'greater_than',
            'between',
            'not_between',
            'contains',
            'doesnt_contain',
            'starts_with',
            'ends_with',
            'in_the_past',
            'in_the_next',
            'in_the_peroid',
            'less_than_count',
            'greater_than_count',
            'equal_to_count',
            'not_equal_to_count',
            'on',
            'before',
            'after',
            'between2_date',
            'today',
            'yesterday',
            'tomorrow',

            'this_week',
            'this_month',
            'this_year',

            'last_week',
            'last_month',
            'last_year',

            'next_week',
            'next_month',
            'next_year',

            'last_7_days',
            'last_30_days',
            'last_60_days',
            'last_90_days',
            'last_120_days',

            'last_6_months',
            'last_12_months',

            'next_7_days',
            'next_30_days',
            'next_60_days',
            'next_90_days',
            'next_120_days',

            'next_6_months',
            'next_12_months',
        ]);
    }
}
