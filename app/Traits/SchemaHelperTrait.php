<?php

namespace App\Traits;

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

/**
 * Schema Helper Trait for modern Laravel schema operations
 * Replaces deprecated Doctrine DBAL methods
 */
trait SchemaHelperTrait
{
    /**
     * Get table columns with error handling
     *
     * @param string $table
     * @return array
     */
    protected function getTableColumns(string $table): array
    {
        try {
            if (!Schema::hasTable($table)) {
                return [];
            }

            $columns = Schema::getColumns($table);
            $columnTypes = [];

            foreach ($columns as $column) {
                $columnName = $column['name'] ?? null;
                if ($columnName && Schema::hasColumn($table, $columnName)) {
                    $columnTypes[$columnName] = Schema::getColumnType($table, $columnName);
                }
            }

            return $columnTypes;
        } catch (\Exception $e) {
            Log::error("Failed to get columns for table {$table}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all database tables
     *
     * @return array
     */
    protected function getAllTables(): array
    {
        try {
            return Schema::getTableListing();
        } catch (\Exception $e) {
            Log::error("Failed to get table listing: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Check if column exists safely
     *
     * @param string $table
     * @param string $column
     * @return bool
     */
    protected function columnExists(string $table, string $column): bool
    {
        try {
            return Schema::hasTable($table) && Schema::hasColumn($table, $column);
        } catch (\Exception $e) {
            Log::error("Failed to check column existence for {$table}.{$column}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get column type safely
     *
     * @param string $table
     * @param string $column
     * @return string|null
     */
    protected function getColumnType(string $table, string $column): ?string
    {
        try {
            if ($this->columnExists($table, $column)) {
                return Schema::getColumnType($table, $column);
            }
            return null;
        } catch (\Exception $e) {
            Log::error("Failed to get column type for {$table}.{$column}: " . $e->getMessage());
            return null;
        }
    }
}
