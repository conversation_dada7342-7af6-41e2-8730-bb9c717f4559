<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Modules\Review\Models\Review;

trait HasReviews
{
    use HasRelationships;

    private $reviewClass = null;

    public static function bootHasReviews()
    {
        static::deleting(function ($model) {
            if (method_exists($model, 'isForceDeleting') && !$model->isForceDeleting()) {
                return;
            }
            $model->reviews()->detach();
        });
    }

    /**
     * A model may have multiple reviews.
     */
    public function reviews(): BelongsToMany
    {
        return $this->morphToMany(
            Review::class,
            'model',
            'model_has_reviews',
            'model_id',
        );
    }

    /**
     * Revoke the given role from the model.
     *
     * @param  string|int|Review|\BackedEnum  $review
     */
    public function removeReview($review): HasReviews
    {
        $this->reviews()->detach($this->getStoredReview($review));

        $this->unsetRelation('reviews');

        return $this;
    }

    /**
     * @param $review
     * @return Review
     */
    protected function getStoredReview($review): Review
    {
        if (!$this->getReviewClass()::find($review)) {

            dd($this->getReviewClass()::find($review), $review);
        }
        if (is_int($review)) {
            return $this->getReviewClass()::find($review);
        }
        dd($review);
        return $review;
    }

    public function getReviewClass(): string
    {
        if (!$this->serviceClass) {
            $this->serviceClass = Review::class;
        }

        return $this->serviceClass;
    }

    /**
     * Remove all current reviews and set the given ones.
     *
     * @param  string|int|array|Review|Collection|\BackedEnum  ...$reviews
     * @return $this
     */
    public function syncReviews(...$reviews)
    {
        if ($this->getModel()->exists) {
            $this->collectReviews($reviews);
            $this->reviews()->detach();
            $this->setRelation('reviews', collect());
        }

        return $this->assignReview($reviews);
    }

    /**
     * Returns array of role ids
     *
     * @param  string|int|array|Review|Collection|\BackedEnum  $reviews
     */
    private function collectReviews(...$reviews): array
    {
        return collect($reviews)
            ->flatten()
            ->reduce(function ($array, $review) {
                if (empty($review)) {
                    return $array;
                }

                $review = $this->getStoredReview($review);
                if (!$review instanceof Review) {
                    return $array;
                }

                if (!in_array($review->getKey(), $array)) {
                    $array[] = $review->getKey();
                }

                return $array;
            }, []);
    }

    /**
     * Assign the given role to the model.
     *
     * @param $review
     * @return $this
     */
    public function assignReview($review)
    {
        $reviews = $this->collectReviews($review);

        $model = $this->getModel();

        if ($model->exists) {
            $currentReviews = $this->reviews->map(fn($review) => $review->getKey())->toArray();
            $this->reviews()->attach(array_diff($reviews, $currentReviews), ['created_at' => now()]);
            $model->unsetRelation('reviews');
        } else {
            $class = \get_class($model);
            $saved = false;

            $class::saved(
                function ($object) use ($reviews, $model, &$saved) {
                    if ($saved || $model->getKey() != $object->getKey()) {
                        return;
                    }
                    $model->reviews()->attach($reviews);
                    $model->unsetRelation('reviews');
                    $saved = true;
                }
            );
        }

        return $this;
    }

    /**
     * Determine if the model has any of the given role(s).
     *
     * Alias to hasReview() but without Guard controls
     *
     * @param  string|int|array|Review|Collection|\BackedEnum  $reviews
     */
    public function hasAnyReview(...$reviews): bool
    {
        return $this->hasReview($reviews);
    }

    /**
     * Determine if the model has (one of) the given role(s).
     *
     * @param  string|int|array|Review|Collection|\BackedEnum  $reviews
     */
    public function hasReview($reviews): bool
    {
        $this->loadMissing('reviews');

        if (is_string($reviews) && strpos($reviews, '|') !== false) {
            $reviews = $this->convertPipeToArray($reviews);
        }

        if ($reviews instanceof \BackedEnum) {
            $reviews = $reviews->value;

            return $this->reviews
                ->pluck('name')
                ->contains(function ($name) use ($reviews) {
                    /** @var string|\BackedEnum $name */
                    if ($name instanceof \BackedEnum) {
                        return $name->value == $reviews;
                    }

                    return $name == $reviews;
                });
        }

        if (is_int($reviews)) {
            $key = (new ($this->getReviewClass())())->getKeyName();

            return $this->reviews->contains($key, $reviews);
        }

        if (is_string($reviews)) {
            return $this->reviews->contains('name', $reviews);
        }

        if ($reviews instanceof Review) {
            return $this->reviews->contains($reviews->getKeyName(), $reviews->getKey());
        }

        if (is_array($reviews)) {
            foreach ($reviews as $review) {
                if ($this->hasReview($review)) {
                    return true;
                }
            }

            return false;
        }

        if ($reviews instanceof Collection) {
            return $reviews->intersect($this->reviews)->isNotEmpty();
        }

        throw new \TypeError('Unsupported type for $reviews parameter to hasReview().');
    }

    /**
     * @return Collection
     */
    public function getReviewNames(): Collection
    {
        $this->loadMissing('reviews');

        return $this->reviews->pluck('name');
    }
}
