<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Modules\Service\Models\Service;

trait HasServices
{
    use HasRelationships;

    private $serviceClass = null;

    public static function bootHasServices()
    {
        static::deleting(function ($model) {
            if (method_exists($model, 'isForceDeleting') && !$model->isForceDeleting()) {
                return;
            }
            $model->services()->detach();
        });
    }

    /**
     * A model may have multiple services.
     */
    public function services(): BelongsToMany
    {
        return $this->morphToMany(
            Service::class,
            'model',
            'model_has_services',
            'model_id',
        )->withPivot('qty', 'price');
    }

    /**
     * Revoke the given role from the model.
     *
     * @param  string|int|Service|\BackedEnum  $service
     */
    public function removeService($service): HasServices
    {
        $this->services()->detach($this->getStoredService($service));

        $this->unsetRelation('services');

        return $this;
    }

    /**
     * @param $service
     * @return Service
     */
    protected function getStoredService($service): Service
    {
        if (!$this->getServiceClass()::find($service)) {

            dd($this->getServiceClass()::find($service), $service);
        }
        if (is_int($service)) {
            return $this->getServiceClass()::find($service);
        }
        dd($service);
        return $service;
    }

    public function getServiceClass(): string
    {
        if (!$this->serviceClass) {
            $this->serviceClass = Service::class;
        }

        return $this->serviceClass;
    }

    /**
     * Remove all current services and set the given ones.
     *
     * @param  string|int|array|Service|Collection|\BackedEnum  ...$services
     * @return $this
     */
    public function syncServices(...$services)
    {
        if ($this->getModel()->exists) {
            $this->collectServices($services);
            $this->services()->detach();
            $this->setRelation('services', collect());
        }

        return $this->assignService($services);
    }

    /**
     * Returns array of role ids
     *
     * @param  string|int|array|Service|Collection|\BackedEnum  $services
     */
    private function collectServices(...$services): array
    {
        return collect($services)
            ->flatten()
            ->reduce(function ($array, $service) {
                if (empty($service)) {
                    return $array;
                }

                $service = $this->getStoredService($service);
                if (!$service instanceof Service) {
                    return $array;
                }

                if (!in_array($service->getKey(), $array)) {
                    $array[] = $service->getKey();
                }

                return $array;
            }, []);
    }

    /**
     * Assign the given role to the model.
     *
     * @param $service
     * @param $qty
     * @param $price
     * @return $this
     */
    public function assignService($service, $qty, $price)
    {
        $services = $this->collectServices($service);

        $model = $this->getModel();

        if ($model->exists) {
            $currentServices = $this->services->map(fn($service) => $service->getKey())->toArray();
            $this->services()->attach(array_diff($services, $currentServices), ['qty' => $qty, 'price' => $price, 'created_at' => now()]);
            $model->unsetRelation('services');
        } else {
            $class = \get_class($model);
            $saved = false;

            $class::saved(
                function ($object) use ($services, $model, &$saved, $qty, $price) {
                    if ($saved || $model->getKey() != $object->getKey()) {
                        return;
                    }
                    $model->services()->attach($services, ['qty' => $qty, 'price' => $price]);
                    $model->unsetRelation('services');
                    $saved = true;
                }
            );
        }

        return $this;
    }

    /**
     * Determine if the model has any of the given role(s).
     *
     * Alias to hasService() but without Guard controls
     *
     * @param  string|int|array|Service|Collection|\BackedEnum  $services
     */
    public function hasAnyService(...$services): bool
    {
        return $this->hasService($services);
    }

    /**
     * Determine if the model has (one of) the given role(s).
     *
     * @param  string|int|array|Service|Collection|\BackedEnum  $services
     */
    public function hasService($services): bool
    {
        $this->loadMissing('services');

        if (is_string($services) && strpos($services, '|') !== false) {
            $services = $this->convertPipeToArray($services);
        }

        if ($services instanceof \BackedEnum) {
            $services = $services->value;

            return $this->services
                ->pluck('name')
                ->contains(function ($name) use ($services) {
                    /** @var string|\BackedEnum $name */
                    if ($name instanceof \BackedEnum) {
                        return $name->value == $services;
                    }

                    return $name == $services;
                });
        }

        if (is_int($services)) {
            $key = (new ($this->getServiceClass())())->getKeyName();

            return $this->services->contains($key, $services);
        }

        if (is_string($services)) {
            return $this->services->contains('name', $services);
        }

        if ($services instanceof Service) {
            return $this->services->contains($services->getKeyName(), $services->getKey());
        }

        if (is_array($services)) {
            foreach ($services as $service) {
                if ($this->hasService($service)) {
                    return true;
                }
            }

            return false;
        }

        if ($services instanceof Collection) {
            return $services->intersect($this->services)->isNotEmpty();
        }

        throw new \TypeError('Unsupported type for $services parameter to hasService().');
    }

    public function getServiceNames(): Collection
    {
        $this->loadMissing('services');

        return $this->services->pluck('name');
    }
}
