<?php

declare(strict_types=1);

namespace App\Support\Contracts;

use App\Support\Enums\FieldType;
use App\Support\Enums\FormLayout;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

/**
 * Interface for column field management in CRM system.
 * Defines standard methods for handling dynamic form fields and database columns.
 */
interface ColumnFieldsInterface
{
    /**
     * Get all column fields for a specific model/module.
     */
    public function getFields(string $module): Collection;

    /**
     * Get column fields grouped by section.
     */
    public function getFieldsBySection(string $module): Collection;

    /**
     * Get column fields filtered by field type.
     */
    public function getFieldsByType(string $module, FieldType $fieldType): Collection;

    /**
     * Get required fields for a module.
     */
    public function getRequiredFields(string $module): Collection;

    /**
     * Get optional fields for a module.
     */
    public function getOptionalFields(string $module): Collection;

    /**
     * Get fields that are visible in list views.
     */
    public function getListViewFields(string $module): Collection;

    /**
     * Get fields that are visible in detail views.
     */
    public function getDetailViewFields(string $module): Collection;

    /**
     * Get fields that are editable in forms.
     */
    public function getEditableFields(string $module): Collection;

    /**
     * Get fields that are searchable.
     */
    public function getSearchableFields(string $module): Collection;

    /**
     * Get field configuration by field name.
     */
    public function getFieldConfig(string $module, string $fieldName): ?array;

    /**
     * Validate field data against field configuration.
     */
    public function validateFieldData(string $module, string $fieldName, mixed $value): bool;

    /**
     * Get validation rules for all fields in a module.
     */
    public function getValidationRules(string $module): array;

    /**
     * Get field labels for a module.
     */
    public function getFieldLabels(string $module): array;

    /**
     * Transform field value for display.
     */
    public function transformForDisplay(string $module, string $fieldName, mixed $value): mixed;

    /**
     * Transform field value for storage.
     */
    public function transformForStorage(string $module, string $fieldName, mixed $value): mixed;

    /**
     * Get field options/choices for select fields.
     */
    public function getFieldOptions(string $module, string $fieldName): array;

    /**
     * Check if a field exists in a module.
     */
    public function hasField(string $module, string $fieldName): bool;

    /**
     * Get the preferred form layout for a module.
     */
    public function getFormLayout(string $module): FormLayout;

    /**
     * Get field ordering for forms.
     */
    public function getFieldOrder(string $module): array;

    /**
     * Get field grouping/sections configuration.
     */
    public function getFieldSections(string $module): array;

    /**
     * Check if a field is conditionally visible.
     */
    public function isFieldVisible(string $module, string $fieldName, Model $record = null): bool;

    /**
     * Get dependent fields for conditional logic.
     */
    public function getDependentFields(string $module, string $fieldName): array;

    /**
     * Get computed field value.
     */
    public function getComputedValue(string $module, string $fieldName, Model $record): mixed;
}
