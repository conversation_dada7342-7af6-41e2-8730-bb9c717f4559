<?php

declare(strict_types=1);

namespace App\Support\Enums;

/**
 * Field types used in CRM column definitions.
 */
enum FieldType: string
{
    case TEXT = 'text';
    case EMAIL = 'email';
    case PASSWORD = 'password';
    case NUMBER = 'number';
    case PHONE = 'phone';
    case URL = 'url';
    case TEXTAREA = 'textarea';
    case SELECT = 'select';
    case MULTI_SELECT = 'multi_select';
    case CHECKBOX = 'checkbox';
    case RADIO = 'radio';
    case DATE = 'date';
    case DATETIME = 'datetime';
    case TIME = 'time';
    case FILE = 'file';
    case IMAGE = 'image';
    case RICH_TEXT = 'rich_text';
    case JSON = 'json';
    case BOOLEAN = 'boolean';
    case CURRENCY = 'currency';
    case PERCENTAGE = 'percentage';
    case COLOR = 'color';
    case RANGE = 'range';

    /**
     * Get human-readable label for the field type
     */
    public function label(): string
    {
        return match ($this) {
            self::TEXT => 'Text',
            self::EMAIL => 'Email',
            self::PASSWORD => 'Password',
            self::NUMBER => 'Number',
            self::PHONE => 'Phone',
            self::URL => 'URL',
            self::TEXTAREA => 'Textarea',
            self::SELECT => 'Select Dropdown',
            self::MULTI_SELECT => 'Multi Select',
            self::CHECKBOX => 'Checkbox',
            self::RADIO => 'Radio Button',
            self::DATE => 'Date',
            self::DATETIME => 'Date & Time',
            self::TIME => 'Time',
            self::FILE => 'File Upload',
            self::IMAGE => 'Image Upload',
            self::RICH_TEXT => 'Rich Text Editor',
            self::JSON => 'JSON Data',
            self::BOOLEAN => 'Boolean',
            self::CURRENCY => 'Currency',
            self::PERCENTAGE => 'Percentage',
            self::COLOR => 'Color Picker',
            self::RANGE => 'Range Slider',
        };
    }

    /**
     * Get validation rules for this field type
     */
    public function getValidationRules(): array
    {
        return match ($this) {
            self::EMAIL => ['email'],
            self::NUMBER, self::CURRENCY, self::PERCENTAGE => ['numeric'],
            self::URL => ['url'],
            self::DATE => ['date'],
            self::DATETIME => ['date'],
            self::TIME => ['date_format:H:i'],
            self::BOOLEAN, self::CHECKBOX => ['boolean'],
            self::FILE, self::IMAGE => ['file'],
            self::JSON => ['json'],
            self::PHONE => ['regex:/^[\+]?[1-9][\d]{0,15}$/'],
            default => ['string'],
        };
    }

    /**
     * Check if field type supports options/choices
     */
    public function hasOptions(): bool
    {
        return in_array($this, [
            self::SELECT,
            self::MULTI_SELECT,
            self::RADIO,
            self::CHECKBOX
        ]);
    }

    /**
     * Check if field type is for file uploads
     */
    public function isFileType(): bool
    {
        return in_array($this, [self::FILE, self::IMAGE]);
    }

    /**
     * Get HTML input type attribute value
     */
    public function getHtmlInputType(): string
    {
        return match ($this) {
            self::PASSWORD => 'password',
            self::EMAIL => 'email',
            self::NUMBER => 'number',
            self::PHONE => 'tel',
            self::URL => 'url',
            self::DATE => 'date',
            self::DATETIME => 'datetime-local',
            self::TIME => 'time',
            self::FILE, self::IMAGE => 'file',
            self::COLOR => 'color',
            self::RANGE => 'range',
            self::CHECKBOX => 'checkbox',
            self::RADIO => 'radio',
            default => 'text',
        };
    }

    /**
     * Get all field types suitable for database columns
     */
    public static function getDatabaseTypes(): array
    {
        return [
            self::TEXT,
            self::EMAIL,
            self::NUMBER,
            self::PHONE,
            self::URL,
            self::DATE,
            self::DATETIME,
            self::TIME,
            self::BOOLEAN,
            self::CURRENCY,
            self::PERCENTAGE,
            self::JSON,
        ];
    }
}
