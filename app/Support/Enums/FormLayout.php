<?php

declare(strict_types=1);

namespace App\Support\Enums;

/**
 * Section types for form layout organization.
 */
enum SectionType: string
{
    case GENERAL = 'general_information';
    case DESCRIPTION = 'description_information';
    case SUBFORM = 'subform';
    case CUSTOM = 'custom';

    /**
     * Get default sections for CRM forms.
     */
    public static function defaults(): array
    {
        return [
            self::GENERAL->value,
            self::DESCRIPTION->value,
        ];
    }
}

/**
 * Column layout positions within sections.
 */
enum ColumnPosition: string
{
    case COL1 = 'col_1';
    case COL2 = 'col_2';
    case FULL_WIDTH = 'full_width';

    /**
     * Get available column positions.
     */
    public static function available(): array
    {
        return [
            self::COL1->value,
            self::COL2->value,
            self::FULL_WIDTH->value,
        ];
    }
}

/**
 * Data source origin for columns.
 */
enum DataSource: string
{
    case SELF = 'self';
    case RELATION = 'relation';
    case COMPUTED = 'computed';
    case EXTERNAL = 'external';
}

/**
 * Form layout types for rendering dynamic forms.
 */
enum FormLayout: string
{
    case SINGLE_COLUMN = 'single_column';
    case TWO_COLUMN = 'two_column';
    case THREE_COLUMN = 'three_column';
    case FOUR_COLUMN = 'four_column';
    case TABS = 'tabs';
    case ACCORDION = 'accordion';
    case WIZARD = 'wizard';
    case CARD = 'card';
    case INLINE = 'inline';
    case HORIZONTAL = 'horizontal';
    case VERTICAL = 'vertical';

    /**
     * Get human-readable label for the layout type
     */
    public function label(): string
    {
        return match ($this) {
            self::SINGLE_COLUMN => 'Single Column',
            self::TWO_COLUMN => 'Two Columns',
            self::THREE_COLUMN => 'Three Columns',
            self::FOUR_COLUMN => 'Four Columns',
            self::TABS => 'Tabbed Layout',
            self::ACCORDION => 'Accordion Layout',
            self::WIZARD => 'Multi-step Wizard',
            self::CARD => 'Card Layout',
            self::INLINE => 'Inline Layout',
            self::HORIZONTAL => 'Horizontal Layout',
            self::VERTICAL => 'Vertical Layout',
        };
    }

    /**
     * Get CSS classes for the layout
     */
    public function getCssClasses(): string
    {
        return match ($this) {
            self::SINGLE_COLUMN => 'grid grid-cols-1 gap-4',
            self::TWO_COLUMN => 'grid grid-cols-1 md:grid-cols-2 gap-4',
            self::THREE_COLUMN => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4',
            self::FOUR_COLUMN => 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4',
            self::TABS => 'tabs-container',
            self::ACCORDION => 'accordion-container',
            self::WIZARD => 'wizard-container',
            self::CARD => 'card-container space-y-4',
            self::INLINE => 'flex flex-wrap gap-4',
            self::HORIZONTAL => 'space-y-4',
            self::VERTICAL => 'flex flex-col space-y-4',
        };
    }

    /**
     * Check if layout supports responsive behavior
     */
    public function isResponsive(): bool
    {
        return in_array($this, [
            self::SINGLE_COLUMN,
            self::TWO_COLUMN,
            self::THREE_COLUMN,
            self::FOUR_COLUMN,
            self::INLINE,
        ]);
    }

    /**
     * Check if layout requires JavaScript
     */
    public function requiresJavaScript(): bool
    {
        return in_array($this, [
            self::TABS,
            self::ACCORDION,
            self::WIZARD,
        ]);
    }

    /**
     * Get maximum recommended fields for this layout
     */
    public function getMaxRecommendedFields(): int
    {
        return match ($this) {
            self::INLINE => 5,
            self::SINGLE_COLUMN => 20,
            self::TWO_COLUMN => 30,
            self::THREE_COLUMN => 40,
            self::FOUR_COLUMN => 50,
            self::TABS, self::ACCORDION => 100,
            self::WIZARD => 200,
            default => 25,
        };
    }

    /**
     * Get all layouts suitable for mobile devices
     */
    public static function getMobileCompatible(): array
    {
        return [
            self::SINGLE_COLUMN,
            self::TABS,
            self::ACCORDION,
            self::WIZARD,
            self::VERTICAL,
        ];
    }
}
