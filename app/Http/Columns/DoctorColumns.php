<?php

namespace App\Http\Columns;

use App\Models\Booking;
use App\Models\Doctor;
use App\Models\User;
use App\Support\BaseColumnFields;

class DoctorColumns extends BaseColumnFields
{
    const DESCRIPTION_SECTION = 'description_information';

    public function __construct($guard = 'web')
    {
        parent::__construct(Doctor::class, $guard);
        $this->addColumn("id")
            ->setFieldsSectionCol(self::COL1)
            ->get();
    }
}
