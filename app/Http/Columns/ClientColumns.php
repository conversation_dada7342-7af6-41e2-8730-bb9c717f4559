<?php

namespace App\Http\Columns;

use App\Models\Booking;
use App\Models\Client;
use App\Models\User;
use App\Support\BaseColumnFields;

class ClientColumns extends BaseColumnFields
{
    const DESCRIPTION_SECTION = 'description_information';

    public function __construct($guard = 'web')
    {
        parent::__construct(Client::class, $guard);
//        $columnsListing = Booking::getColumnsListing();
//        $columns = Booking::getColumns();
        $this->addColumn("id")
            ->setFieldsSectionCol(self::COL1)
            ->get();

        $this->addColumn("order_reference")->get();
        $this->addColumn("name")->setValidation(true)
            ->get();
        $this->addColumn("type")->get();
        $this->addColumn("price")->get();
        $this->addColumn("status")->get();
        $this->addColumn("branch_id")->get();
        $this->addColumn("payment_reference")->get();
        $this->addColumn("payment_status")->get();
        $this->addColumn("payment_method")->get();
        $this->addColumn("is_special")->get();
        $this->addColumn("offer_id")->get();
        $this->addColumn("user_id")->get();
        $this->addColumn("phone")->get();
        $this->addColumn("doctor_id")->get();
        $this->addColumn("payment_type")->get();
        $this->addColumn("type_installment")->get();
        $this->addColumn("attendance_date")->get();
        $this->addColumn("available_time")->get();
        $this->addColumn("note")->get();
        $this->addColumn("created_at")->get();
        $this->addColumn("updated_at")->get();
        $this->addColumn("email")->get();
        $this->addColumn("src")->get();
        $this->addColumn("src_url")->get();
        $this->addColumn("src_form")->get();
//        $this->addColumn("campaign_id")->get();
        $my_staff_users = User::all();
        $this->addVisitsFilterColumns();

//        $this->addColumn('id')
//            ->setFieldsSectionCol(self::COL1)
//            ->setTitle('ID')
//            ->get();
//
//        $this->addColumn('title')
//            ->setTitle('Lead Name')
//            ->setValidation(true)
//            ->get();

//        $this->getFilterRelationsCountColumns();

//        $this->addingCrudInfoColumns();
    }
}
