<?php

namespace App\Http\Columns;

use App\Models\Ticket;
use App\Support\BaseColumnFields;

class TicketColumns extends BaseColumnFields
{
    const DESCRIPTION_SECTION = 'description_information';

    /**
     * @var string
     */
    public $model = null;
    public $permissionName = null;

    public function __construct($guard = 'web')
    {
        parent::__construct(Ticket::class, $guard);

        $this->addColumn("id")->get();
        $this->addColumn("name")->get();
        $this->addColumn("phone")->get();
        $this->addColumn("email")->get();
        $this->addColumn("subject")->get();
        $this->addColumn("topic")->get();
        $this->addColumn("content")->get();
        $this->addColumn("purpose")->get();
        $this->addColumn("")->get();
        $this->addingVisitActivityColumns();
        $this->addingTouchedInfoColumns();
        $this->addingCrudInfoColumns();
    }
}
