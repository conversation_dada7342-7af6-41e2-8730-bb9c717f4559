<?php

namespace App\Http\Columns;

use App\Models\Role;
use App\Support\BaseColumnFields;

class RoleColumns extends BaseColumnFields
{
    /**
     * @var string
     */
    public $model = null;
    public $permissionName = null;

    public function __construct($guard = 'web')
    {
        parent::__construct(Role::class, $guard);
        $this->addColumn("id")->get();
        $this->addColumn("name")->get();
        $this->addColumn("guard_name")->get();
        $this->addingCrudInfoColumns();
    }
}
