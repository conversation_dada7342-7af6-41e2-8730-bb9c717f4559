<?php

namespace App\Http\Columns;

use App\Models\Note;
use App\Support\BaseColumnFields;

class NoteColumns extends BaseColumnFields
{
    const DESCRIPTION_SECTION = 'description_information';

    /**
     * @var string
     */
    public $model = null;
    public $permissionName = null;

    public function __construct($guard = 'web')
    {
        parent::__construct(Note::class, $guard);

        $this->addColumn("id")->get();

        $this->addColumn("content")->get();

        $this->addColumn("noteable_id")->get();

        $this->addColumn("noteable_type")->get();

        $this->addingVisitActivityColumns();
        $this->addingTouchedInfoColumns();
        $this->addingCrudInfoColumns();
    }
}
