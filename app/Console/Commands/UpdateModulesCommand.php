<?php

namespace App\Console\Commands;

use App\Helpers\ModuleHelper;
use App\Models\Module;
use App\Traits\CrmCommandTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class UpdateModulesCommand extends Command
{
    use CrmCommandTrait;

    /**
     * The name and signature of the console command.
     * @var string
     */
    protected $signature = 'crm:modules:update';

    /**
     * The console command description.
     * @var string
     */
    protected $description = 'This command will create a new module if not exists from config/crm_core.php';
    protected $singularNames = [
    ];

    /**
     * Create a new command instance.
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     * @return int
     */
    public function handle()
    {
        $this->commandStartInfo("Update Modules.");

        $modules = ModuleHelper::getCrmCoreModules();

        $this->createOrUpdateModules($modules);

        $this->removeDontUsedModules($modules);

        return 0;
    }

    /**
     * @param $modules
     * @return void
     */
    private function createOrUpdateModules($modules): void
    {
        $no_new_module = true;
        foreach ($modules as $modelName) {
            if ($this->firstOrCreateModule($modelName)) {
                $no_new_module = false;
            }
        }

        if ($no_new_module) {
            $this->warnEnd("Modules already up to date.");
        } else {
            $this->commandEndInfo("Modules Updated Successfully.");
        }
    }

    /**
     * @param $modelName
     * @return bool|int
     */
    function firstOrCreateModule($modelName): bool|int
    {
        $exits = Module::where('class_name', $modelName)->first();
        if ($exits) {
            $module = $exits;
            $original = $module->getOriginal();
            foreach (['slug', 'title', 'table_name', 'icon', 'controller_name', 'class_name',] as $column_name) {
                if ($module->{$column_name} != $original[$column_name] ?? null) {
                    $module->save();
                    $this->info("Update exists Module: {$module->title}");
                    break;
                }
            }
            return false;
        }
        $slug = Str::slug(Str::singular(Str::camel(class_basename($modelName))));
        $module = new Module();
        $module->slug = $slug;
        $module->title = $modelName::getModuleTitle();
        $module->table_name = $modelName::getTableName();
        $module->icon = $modelName::getIcon();
        $module->controller_name = $modelName::getController();
        $module->class_name = $modelName::getModuleClass();
        $module->save();
        $this->info("Create new Module: {$module->title}");
        return true;
    }

    /**
     * @param $modules
     * @return void
     */
    private function removeDontUsedModules($modules)
    {
//        foreach (Module::all() as $module) {
//            if (!in_array($module->table_name, collect($modules)->pluck('title')->toArray()) || $module->class_name == null) {
//                $module_name = $module->name;
//                if (in_array(Str::lower($this->ask("Do you want to delete {$module->name} from modules in db? (yes/no).", 'no')), ['yes', 'y'])) {
//                    $module->forceDelete();
//                    $this->info("{$module_name} module has been deleted successfully.");
//                }
//            }
//        }
    }
}
