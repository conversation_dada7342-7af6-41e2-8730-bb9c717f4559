<?php

namespace App\Console\Commands;

use App\Helpers\ModuleHelper;
use App\Models\Module;
use App\Models\Permission;
use App\Models\Role;
use App\Traits\CrmCommandTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class UpdatePermissionsCommand extends Command
{
    use CrmCommandTrait;

    /**
     * The name and signature of the console command.
     * @var string
     */
    protected $signature = 'crm:permissions:update
                            {--f|--field : update all field permissions for all modules .}
                            {--p|--profile : update all field permissions for all modules .}';

    /**
     * The console command description.
     * @var string
     */
    protected $description = "This command will be update all permissions by removing don't used and create required permissions which not exists before";

    protected $sleep_counter = 0;

    /**
     * Create a new command instance.
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     * @return int
     */
    public function handle()
    {
        $this->commandStartInfo("Update Permissions.");

        // Create Module Permission Actions (View,Create,Edit,Export,.....)
        $modules = ModuleHelper::getAllModules();
        $permission_fields = collect();
        foreach ($modules as $module) {
            $moduleName = $module->getSlug();
            $modelClassName = $module->getModuleClass();
            if (!class_exists($modelClassName)) {
                $this->warn("Model not exists: {$module->title}");
                $module->delete();
                $this->warn("Delete Module: {$module->name}");
                continue;
            }
            if (defined("$modelClassName::PERMISSION_ACTION")) {
                foreach ($modelClassName::PERMISSION_ACTION as $permissionName => $value) {
                    $permission_fields = $permission_fields->add("{$moduleName}.{$permissionName}");
                    $this->newItem("{$moduleName}.{$permissionName}", $module, 'profile');
                }
            }
        }
        // Create Module Field Permissions (Lead.id.readonly, Lead.title.readwrite,.....)
        $modules = ModuleHelper::AvailableFieldPermissionModules();
dd($modules);
        foreach ($modules as $module) {
            $moduleName = $module->name;
            $moduleClass = $module->model;
            if (!class_exists($moduleClass)) {
                $this->warn($moduleClass.' not exists');
                continue;
            }
            if (isset($moduleClass::$columns)) {
                $moduleColsClass = $moduleClass::$columns;
            } else {
                $this->warn($moduleClass.' columns class not exists');
                continue;
            }
            if (class_exists($moduleColsClass)) {
                $READ_WRITE = Permission::FIELD_PERMISSION_READ_WRITE;
                $READ_ONLY = Permission::FIELD_PERMISSION_READ_ONLY;
                $DONT_SHOW = Permission::FIELD_PERMISSION_DONT_SHOW;

                $ob = new $moduleColsClass;
                // Create Permission Columns in DB
                $fields = $ob->permissionColumns();
                foreach ($fields->pluck('name') as $field) {
                    $per_names = [
                        "{$moduleName}.{$field}.{$READ_WRITE}",
                        "{$moduleName}.{$field}.{$READ_ONLY}",
                        "{$moduleName}.{$field}.{$DONT_SHOW}",
                    ];
                    $permission_fields = $permission_fields->merge(collect($per_names));
                    foreach ($per_names as $per_name) {
                        $this->newItem($per_name, $module);
                    }
                }

                // Remove Not Permission Columns: (filter and hidden) columns from DB
                $fields = $ob->notPermissionColumns();
                foreach ($fields->pluck('name') as $field) {
                    $this->dropItemIfExists("{$moduleName}.{$field}.{$READ_WRITE}");
                    $this->dropItemIfExists("{$moduleName}.{$field}.{$READ_ONLY}");
                    $this->dropItemIfExists("{$moduleName}.{$field}.{$DONT_SHOW}");
                }

                // Remove Not Used Permission Columns: When this columns removed from cols classes remove it from db
                $notUsedModulePermissions = Permission::where('module_id', $module->id)->whereNotIn('name', $permission_fields->toArray())->get()->pluck('name');
                if (is_countable($notUsedModulePermissions) and count($notUsedModulePermissions)) {
                    foreach ($notUsedModulePermissions as $permission) {
                        $this->dropItemIfExists($permission);
                    }
                }
            }
        }


        if ($this->option('field')) {
            /*
             * give default field permissions as readwrite to all roles when the role
             *  don't have any permission related to field
             */
            $this->giveDefaultFieldPermissionToAllRoles();

        } else {

            // give field permissions as readwrite to developers
            $this->giveTheRolePermissions('developer');
            $this->giveTheRolePermissions('tester');
            $this->giveTheRolePermissions('ceo');
        }

        $this->commandEndInfo("Permissions Updated Successfully.");

        $this->call('crm:clear');

        return 0;
    }

    /**
     * @param      $name
     * @param      $module
     * @param  null  $permission_type
     */
    function newItem($name, $module, $permission_type = null)
    {

        if (!Permission::where('name', $name)->first()) {
            $this->sleep_counter++;
            if ($this->sleep_counter % 100 == 0) {
                $this->warn('sleeping to 5 second...');
                sleep(5);
            }
            $item = new Permission();
            $item->name = $name;
            $name = str_replace('_', ' ', $name);
            $name = str_replace('.', ' ', $name);
            $item->title = ucwords($name);
            $item->permission_type = $permission_type ?? 'field';
            $item->module_id = optional($module)->id;
            $item->save();
            $this->line("permission has been created successfully: <info>{$item->name}</info>");
        }
    }

    /**
     * @param      $name
     */
    function dropItemIfExists($name)
    {
        $field_exists = Permission::where('name', $name)->first();
        if ($field_exists) {
            $field_exists->delete();
            $this->line("permission has been <comment>deleted</comment> successfully: <info>{$field_exists->name}</info>");
        }
    }

    /**
     * @param  string  $type
     * @return void
     */
    private function giveDefaultFieldPermissionToAllRoles(string $type = 'readWrite')
    {
        // get all field permissions contain selected type like (table.field.type => (Account.id.readWrite))
        $permissions = Permission::select('id', 'name', 'permission_type')
            ->where('permission_type', 'field')
            ->where('name', 'like', "%{$type}%")
            ->get();


//        $developer_ceo_roles = ['ceo', 'developer', 'tester'];
        $developer_ceo_roles = [];

        // get all roles without CEO, Tester and developer
        $roles = \App\Models\Role::whereNotIn('name', $developer_ceo_roles)->get();

        foreach ($roles as $key => $role) {
            $role_permissions_array = $role->permissions->pluck('name')->toArray();

            $give_permissions_array = [];
            foreach ($permissions as $permission) {
                $field_permission_sub_name = str_replace($type, '', $permission->name);

                $permissions_names_array = [
                    $field_permission_sub_name."readOnly",
                    $field_permission_sub_name."dontShow",
                    $field_permission_sub_name."readWrite",
                ];

                $permission_given_to_role = false;
                foreach ($permissions_names_array as $prm_name) {
                    if (in_array($prm_name, $role_permissions_array)) {
                        $permission_given_to_role = true;
                    }
                }

                if (!$permission_given_to_role) {
                    $give_permissions_array[] = $permission->name;
                }
            }

            $prm_count = count($give_permissions_array);
            if (!$prm_count) {
                $this->line("no default permissions to give to <comment>{$role->title}</comment> role.");
                continue;
            }

            $role->givePermissionTo($give_permissions_array);
            $this->line("<info>{$role->title} role:</info> has been given <info>{$prm_count}</info> default permissions as <info>{$type}</info> successfully.");

            if ($key % 3 == 0 and $key) {
                $this->warn('sleeping to 1 second ...........');
                $this->warn('---------------------------------------------------------------------');
                sleep(1);
            }
        }
    }

    /**
     * @param  string  $role_name
     * @return void
     */
    private function giveTheRolePermissions(string $role_name = 'ceo')
    {
        $dontShow = Permission::FIELD_PERMISSION_DONT_SHOW;
        $readOnly = Permission::FIELD_PERMISSION_READ_ONLY;

        $role = Role::findByName(Str::slug($role_name));
        $permissions = Permission::where('name', 'not like', "%$dontShow%")
            ->where('name', 'not like', "%$readOnly%")
            ->get();

        if ($role) {
            $roleTitle = Str::upper($role_name);
            $permissions_array = $permissions->pluck('name')->toArray();
            $role_permissions_array = $role->permissions->pluck('name')->toArray();
            if (!count(array_diff($permissions_array, $role_permissions_array))) {
                $this->line("No default permissions to give to <comment>{$roleTitle}</comment> role.");

                return;
            }

            $role->givePermissionTo($permissions->pluck('name'));
            $prm_count = count($permissions);
            $type = 'readWrite';
            $this->line("<info>{$roleTitle} role:</info> has been given <info>{$prm_count}</info> default permissions as <info>{$type}</info> successfully.");

//            $this->warn('sleeping to 1 second...');
            sleep(1);
        } else {
            $this->warn("Role Not Found.");
        }
    }
}
