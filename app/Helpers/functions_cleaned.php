<?php

use App\Helpers\CacheHelper;
use App\Helpers\CacheKey;
use App\Helpers\DateHelper;
use App\Helpers\FileHelper;
use App\Helpers\HtmlHelper;
use App\Helpers\AssetHelper;
use App\Helpers\DatatableHelper;
use App\Helpers\AvatarHelper;
use App\Models\Activity;
use App\Models\CRMAttachment;
use App\Models\DuplicateRequest;
use App\Models\MongoAudit;
use App\Models\Note;
use App\Models\Role;
use App\Models\Setting;
use App\Models\User;
use App\Support\ActivityCols;
use App\Support\BaseColumnFields;
use App\Support\CallCols;
use App\Support\MeetingCols;
use App\Support\TaskCols;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/*
|--------------------------------------------------------------------------
| BACKWARD COMPATIBILITY WRAPPERS
|--------------------------------------------------------------------------
| These functions provide backward compatibility by delegating to the new
| Helper classes. All new code should use the Helper classes directly.
*/

if (!function_exists('show_date')) {
    /**
     * @deprecated Use DateHelper::formatDate() instead
     */
    function show_date($date, $isValueFormat = false)
    {
        return DateHelper::formatDate($date, $isValueFormat);
    }
}

if (!function_exists('show_time')) {
    /**
     * @deprecated Use DateHelper::formatTime() instead
     */
    function show_time($date, $isValueFormat = false)
    {
        return DateHelper::formatTime($date, $isValueFormat);
    }
}

if (!function_exists('show_datetime')) {
    /**
     * @deprecated Use DateHelper::formatDateTime() instead
     */
    function show_datetime($datetime, $isValueFormat = false)
    {
        return DateHelper::formatDateTime($datetime, $isValueFormat);
    }
}

if (!function_exists('file_size')) {
    /**
     * @deprecated Use FileHelper::formatSize() instead
     */
    function file_size($file_size)
    {
        return FileHelper::formatSize($file_size, 'MB', 2);
    }
}

if (!function_exists('file_size_to_kb')) {
    /**
     * @deprecated Use FileHelper::toKb() instead
     */
    function file_size_to_kb($file_size, $from = 'mb')
    {
        return FileHelper::toKb($file_size, $from);
    }
}

if (!function_exists('generate_hash_file_name')) {
    /**
     * @deprecated Use FileHelper::generateHashFileName() instead
     */
    function generate_hash_file_name()
    {
        return FileHelper::generateHashFileName();
    }
}

if (!function_exists('unlinkOldFile')) {
    /**
     * @deprecated Use FileHelper::deleteFile() instead
     */
    function unlinkOldFile($fieldName, $public_file_dir)
    {
        return FileHelper::deleteFile($fieldName, $public_file_dir, 'public');
    }
}

if (!function_exists('isJson')) {
    /**
     * @deprecated Use HtmlHelper::isJson() instead
     */
    function isJson($string, $return_data = false)
    {
        return HtmlHelper::isJson($string, $return_data);
    }
}

if (!function_exists('show_tooltip_datatable_link')) {
    /**
     * @deprecated Use DatatableHelper::createTooltipLink() instead
     */
    function show_tooltip_datatable_link($url, $text, $textLimit, $title = null, $markColor = null)
    {
        return DatatableHelper::createTooltipLink($url, $text, $textLimit, $title, $markColor);
    }
}

if (!function_exists('show_tooltip_datatable_text')) {
    /**
     * @deprecated Use DatatableHelper::createTooltipText() instead
     */
    function show_tooltip_datatable_text($text, $textLimit, $markColor = null)
    {
        return DatatableHelper::createTooltipText($text, $textLimit, $markColor);
    }
}

if (!function_exists('get_avatar_image')) {
    /**
     * @deprecated Use AvatarHelper::image() instead
     */
    function get_avatar_image($name, $size = 64)
    {
        return AvatarHelper::image($name, $size);
    }
}

/*
|--------------------------------------------------------------------------
| REMAINING UTILITY FUNCTIONS
|--------------------------------------------------------------------------
| These functions are still used and haven't been moved to Helper classes
*/

if (!function_exists('show_with_tooltip')) {
    /**
     * Render a string with a tooltip
     * @param string $title
     * @return string
     */
    function show_with_tooltip($title)
    {
        return HtmlHelper::truncateWithTooltip($title, 50);
    }
}

if (!function_exists('titleLink')) {
    /**
     * Render a title link or span with tooltip
     * @param string $prefix
     * @param object $row
     * @param bool $user_can_edit
     * @param string|null $attr
     * @param string|null $_type
     * @return string
     */
    function titleLink($prefix, $row, $user_can_edit, $attr = null, $_type = null)
    {
        $attr = $attr ?? 'model_title';
        $text = $row->{$attr} ?? '';

        if ($user_can_edit) {
            $url = "{$prefix}/{$row->id}";
            return HtmlHelper::link($url, $text, ['title' => "View {$attr}"]);
        }

        return HtmlHelper::escape($text);
    }
}

if (!function_exists('getTransValues')) {
    /**
     * @param $row
     * @param $attr
     * @return string
     */
    function getTransValues($row, $attr)
    {
        $_title = $row->{$attr};
        return show_with_tooltip($_title);
    }
}

if (!function_exists('close_task_button')) {
    function close_task_button($name, $row, $is_can, $moduleClass = null, $icon = null, $url = null)
    {
        $rowId = $row ? $row->id : '';
        $slugName = str_replace('_', '-', $name);
        $class = "btn-{$slugName}-table-item btn-{$slugName}";
        $singularName = Str::singular($moduleClass);
        $moduleClass = str_replace('_', '-', $moduleClass);
        $class = $moduleClass ? "{$class}-{$moduleClass}" : $class;

        $output = "";
        if ($is_can) {
            $title = hidden_sm_text(__('crm_core::locale.buttons.close_task'));
            $output .= "<a href='javascript:void(0)' class='btn btn-xs btn-outline-success btn-close-task-table-item {$class}' id='{$rowId}' data-id='{$rowId}' data-content='{$row->content}' data-modal-name='{$moduleClass}' data-singular-name='{$singularName}' title='" . __('crm_core::locale.buttons.close_task') . "'><i class='bx bx-check'></i></a>";
        }

        return $output;
    }
}

if (!function_exists('editorInfo')) {
    /**
     * @param $page
     * @return string
     */
    function editorInfo($page)
    {
        $output = '';
        $creator = $page->createdBy ? $page->createdBy->model_title : ' System ';
        $output .= "<p class='editor-info'>";
        $creatorTitle = __('crm_core::locale.editor_created_by', ['name' => $creator]);
        $createdAtShowTitle = __('crm_core::locale.editor_created_at', ['date' => DateHelper::formatDateTime($page->created_at, false)]);
        $output .= " <span class='text-capitalize' data-toggle='tooltip' title='$creatorTitle'> <i class='bx bx-plus-circle'></i> " . hidden_sm_text($creator) . " </span>";
        $output .= " - <span data-toggle='tooltip' title='$createdAtShowTitle'> <i class='bx bx-calendar-plus'></i> " . hidden_sm_text(DateHelper::formatDate($page->created_at)) . " </span>";

        $editor = optional($page->modifiedBy)->model_title;
        if ($editor != null) {
            $editorTitle = __('crm_core::locale.editor_modified_by', ['name' => $editor]);
            $updatedAtShowTitle = __('crm_core::locale.editor_modified_at', ['date' => DateHelper::formatDateTime($page->updated_at, false)]);
            $output .= "<br> <span class='text-capitalize' data-toggle='tooltip' title='$editorTitle'> <i class='bx bx-edit'></i> " . hidden_sm_text($editor) . " </span>";
            $output .= " - <span data-toggle='tooltip' title='$updatedAtShowTitle'> <i class='bx bx-calendar'></i> " . hidden_sm_text(DateHelper::formatDate($page->updated_at)) . " </span>";
        }
        $output .= '</p>';

        return $output;
    }
}

if (!function_exists('trashInfo')) {
    /**
     * @param $page
     * @return string
     */
    function trashInfo($page)
    {
        $output = '';
        $deletedBy = $page->deletedBy ? $page->deletedBy->model_title : ' System ';
        $output .= "<p class='editor-info'>";
        $creatorTitle = __('crm_core::locale.editor_deleted_by', ['name' => $deletedBy]);
        $createdAtTitle = __('crm_core::locale.editor_deleted_at', ['date' => DateHelper::formatDate($page->deleted_at)]);
        $output .= " <span class='text-capitalize' data-toggle='tooltip' title='$creatorTitle'> <i class='bx bx-plus-circle'></i> " . hidden_sm_text($creatorTitle) . " </span>";
        $output .= " - <span data-toggle='tooltip' title='$createdAtTitle'> <i class='bx bx-calendar-plus'></i> " . hidden_sm_text($createdAtTitle) . " </span>";
        $output .= '</p>';

        return $output;
    }
}

if (!function_exists('trashActionLinks')) {
    /**
     * @param $row
     * @param $user_can_restore
     * @param $user_can_force_delete
     * @return string
     */
    function trashActionLinks($row, $user_can_restore = true, $user_can_force_delete = true)
    {
        $output = "<div class='btn-group'>
                <button type='button' class='btn btn-xs btn-dark dropdown-toggle' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'><i class='bx bx-spreadsheet'></i></button>
              <div class='dropdown-menu'>";
        if (auth()->check()) {
            $output .= dropdown_item('restore', $row, $user_can_restore);
            $output .= dropdown_item('force_delete', $row, $user_can_force_delete);
        }
        $output .= "</div></div>";

        return $output;
    }
}

if (!function_exists('hidden_sm_text')) {
    /**
     * @param $data
     * @param null $className
     * @return string
     */
    function hidden_sm_text($data, $className = null)
    {
        $className = $className ?? "d-xl-inline-block d-lg-inline-block d-md-inline-block d-none";
        return "<span class='{$className}'> {$data} </span>";
    }
}

if (!function_exists('fancyImageLink')) {
    /**
     * @param $prefix
     * @param $imageName
     * @param int $width
     * @param null $alt
     * @param null $className
     * @return string
     */
    function fancyImageLink($prefix, $imageName, $width = 100, $alt = null, $className = null)
    {
        $className = $className ?? 'img-thumbnail';
        $height = $className == 'img-circle' ? $width : 'auto';
        $url = asset("f/{$prefix}/{$imageName}");

        if (!Storage::exists("public/{$prefix}/{$imageName}")) {
            return '';
        }

        $output = "<a class='grouped_elements' data-fancybox='group' data-caption='{$imageName}' href='{$url}'>";
        $output .= "<img src='{$url}' class='{$className}' width='{$width}' height='{$height}' alt='{$alt}'/>";
        $output .= "</a>";

        return $output;
    }
}

if (!function_exists('getMenuItem')) {
    function getMenuItem($name)
    {
        $horizontalMenuJson = file_get_contents(public_path('assets/data/menus/horizontal-menu.json'));
        $horizontalMenuData = json_decode($horizontalMenuJson);
        foreach ($horizontalMenuData->menu as $item) {
            if ($item->name == $name) {
                return $item;
            }
            if (isset($item->submenu)) {
                foreach ($item->submenu as $submenu) {
                    if ($submenu->name == $name) {
                        return $submenu;
                    }
                }
            }
        }

        return null;
    }
}

if (!function_exists('jsonOutput')) {
    /**
     * @param $error_array
     * @param $success_output
     * @return array
     */
    function jsonOutput($error_array, $success_output = null)
    {
        return [
            'error' => $error_array,
            'success' => $success_output
        ];
    }
}

if (!function_exists('not_found_item')) {
    function not_found_item($request = null, $isAjax = false)
    {
        if (($request && $request->ajax()) || $isAjax) {
            return response()->json(__('crm_core::locale.alert_msg.not_found'), 404);
        }

        return view('crm_core::errors.404');
    }
}

if (!function_exists('AcceptMiddleware')) {
    /**
     * @return array
     */
    function AcceptMiddleware()
    {
        return [
            'web',
            'admin',
            'api'
        ];
    }
}

if (!function_exists('getArrayValidationErrors')) {
    /**
     * @param $validation
     * @return array
     */
    function getArrayValidationErrors($validation)
    {
        $error_array = [];
        if ($validation) {
            foreach ($validation->messages()->getMessages() as $field_name => $messages) {
                $error_array[] = $messages;
            }
        }

        return $error_array;
    }
}

if (!function_exists('related_info_card')) {
    function related_info_card($row, $row_table_title, $moduleName = null, $cardId = null)
    {
        $cardId = $cardId ?? random_int(100, 999);
        $moduleName = $moduleName ?? $row_table_title;
        $image = $row->image ?? null;
        $title = $row->model_title ?? $row->title ?? $row->name ?? 'N/A';
        $url = $row->url ?? '#';

        return view('crm_core::components.related-info-card', compact(
            'row',
            'row_table_title',
            'moduleName',
            'cardId',
            'image',
            'title',
            'url'
        ))->render();
    }
}

if (!function_exists('list_option_mark')) {
    /**
     * @param $listOptionColor
     * @return string|null
     */
    function list_option_mark($listOptionColor)
    {
        if ($listOptionColor && $listOptionColor != "#000000") {
            return "<span class='list-option-circle' style='background:{$listOptionColor};'></span>";
        }

        return null;
    }
}

if (!function_exists('show_tooltip_datatable_link_blank')) {
    /**
     * @deprecated Consider using DatatableHelper::createTooltipLink() with custom attributes
     */
    function show_tooltip_datatable_link_blank($url, $text, $textLimit, $title = null, $markColor = null)
    {
        $returnText = '';

        if ($markColor) {
            $returnText .= list_option_mark($markColor);
        }

        if ($textLimit) {
            $show_text = Str::limit($text, $textLimit);
            if (Str::length($text) > $textLimit) {
                $returnText .= "<span data-toggle='tooltip' title='{$text}'><a href='{$url}' title='{$title}' target='_blank'>$show_text</a></span>";
            } else {
                $returnText .= "<a href='{$url}' title='{$title}' target='_blank'>{$text}</a>";
            }
        } else {
            $returnText .= "<a href='{$url}' title='{$title}' target='_blank'>{$text}</a>";
        }

        return $returnText;
    }
}

// Additional utility functions that are still needed
// ... (rest of the remaining functions from the original file)
