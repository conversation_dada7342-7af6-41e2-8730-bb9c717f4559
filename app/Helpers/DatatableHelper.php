<?php

namespace App\Helpers;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

/**
 * Helper class for datatable display formatting and rendering.
 */
class DatatableHelper
{
    /**
     * Create a tooltip link for datatable display.
     *
     * @param string|null $url Link URL
     * @param string|null $text Link text
     * @param int|null $textLimit Character limit for text
     * @param string|null $title Tooltip title
     * @param string|null $markColor Optional color mark
     * @return string HTML link with tooltip
     */
    public static function createTooltipLink(?string $url, ?string $text, ?int $textLimit = null, ?string $title = null, ?string $markColor = null): string
    {
        if (empty($text) || empty($url)) {
            return '';
        }

        $html = '';

        // Add color mark if specified
        if ($markColor && function_exists('list_option_mark')) {
            $html .= list_option_mark($markColor);
        }

        // Handle text limiting with tooltip
        if ($textLimit && Str::length($text) > $textLimit) {
            $limitedText = Str::limit($text, $textLimit);
            $html .= sprintf(
                '<span data-toggle="tooltip" title="%s"><a href="%s" title="%s">%s</a></span>',
                htmlspecialchars($text),
                htmlspecialchars($url),
                htmlspecialchars($title ?? ''),
                htmlspecialchars($limitedText)
            );
        } else {
            $html .= sprintf(
                '<a href="%s" title="%s">%s</a>',
                htmlspecialchars($url),
                htmlspecialchars($title ?? ''),
                htmlspecialchars($text)
            );
        }

        return $html;
    }

    /**
     * Create tooltip text for datatable display.
     *
     * @param string|null $text Text to display
     * @param int|null $textLimit Character limit
     * @param string|null $markColor Optional color mark
     * @return string HTML text with tooltip
     */
    public static function createTooltipText(?string $text, ?int $textLimit = null, ?string $markColor = null): string
    {
        if (empty($text)) {
            return '';
        }

        $html = '';

        // Add color mark if specified
        if ($markColor && function_exists('list_option_mark')) {
            $html .= list_option_mark($markColor);
        }

        // Handle text limiting with tooltip
        if ($textLimit && Str::length($text) > $textLimit) {
            $limitedText = Str::limit($text, $textLimit);
            $html .= sprintf(
                '<span data-toggle="tooltip" title="%s">%s</span>',
                htmlspecialchars($text),
                htmlspecialchars($limitedText)
            );
        } else {
            $html .= htmlspecialchars($text);
        }

        return $html;
    }

    /**
     * Create username card display for datatable.
     *
     * @param object $row Data row
     * @param string $url Profile URL
     * @param string $name Username
     * @param string|null $email User email
     * @param int|null $textLimit Text character limit
     * @return string Username card HTML
     */
    public static function createUsernameCard(object $row, string $url, string $name, ?string $email = null, ?int $textLimit = null): string
    {
        $image = $row->avatar ?? null;
        if ($image && isset($row->avatarPath)) {
            $avatarHtml = $row->avatarPath;
        } else {
            $avatarHtml = function_exists('get_avatar_image')
                ? get_avatar_image($name, 40)
                : '<div class="avatar-placeholder"></div>';
        }

        $usernameLink = self::createTooltipLink($url, $name, $textLimit, "Click to open profile");
        $emailLink = $email && function_exists('mailLink') ? mailLink($email) : htmlspecialchars($email ?? '');

        return sprintf(
            '<div class="media">
                <a class="username-card-link" href="%s">
                    <div class="username-card-image avatar mr-1">%s</div>
                    <div class="username-card-info media-body">
                        <h6 class="media-heading mb-0">%s</h6>
                        <small class="username-card-email text-muted">%s</small>
                    </div>
                </a>
            </div>',
            htmlspecialchars($url),
            $avatarHtml,
            $usernameLink,
            $emailLink
        );
    }

    /**
     * Format JSON array for datatable display.
     *
     * @param string $jsonString JSON string
     * @return string Formatted HTML list
     */
    public static function formatJsonForDisplay(string $jsonString): string
    {
        if (!HtmlHelper::isJson($jsonString) || $jsonString === '[null]') {
            return '';
        }

        try {
            $decodedValue = json_decode($jsonString, true) ?? [];
            $listItems = '';

            foreach ($decodedValue as $value) {
                if ($value !== null) {
                    $displayValue = is_array($value) ? implode(', ', $value) : (string)$value;
                    $listItems .= '<li>' . htmlspecialchars($displayValue) . '</li>';
                } else {
                    $listItems .= '<li class="text-muted">-</li>';
                }
            }

            return $listItems ? "<ul class='list-group'>{$listItems}</ul>" : '';

        } catch (\Exception $e) {
            Log::warning("Failed to format JSON for display", [
                'json' => $jsonString,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * Add count suffix to column text if count attribute exists.
     *
     * @param string $column Column text
     * @param object $row Data row
     * @param array $colConfig Column configuration
     * @return string Column text with count
     */
    public static function addCountSuffix(string $column, object $row, array $colConfig): string
    {
        if (empty(trim($column))) {
            return $column;
        }

        $countAttribute = $colConfig['slug'] . '_count';
        if (isset($row->{$countAttribute})) {
            $column .= " ({$row->{$countAttribute}})";
        }

        return $column;
    }

    /**
     * Add prefix and suffix to column text.
     *
     * @param string $column Column text
     * @param object $row Data row
     * @return string Column text with prefix/suffix
     */
    public static function addPrefixSuffix(string $column, object $row): string
    {
        if (empty(trim($column))) {
            return $column;
        }

        $result = $column;

        if (isset($row->prefix)) {
            $result = $row->prefix . $result;
        }

        if (isset($row->suffix)) {
            $result = $result . $row->suffix;
        }

        return $result;
    }
}
