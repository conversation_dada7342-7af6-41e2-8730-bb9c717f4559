<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;

/**
 * Helper class for asset management and versioning.
 */
class AssetHelper
{
    /**
     * Get asset URL with cache-busting version parameter.
     *
     * @param string $path Asset path relative to public directory
     * @return string Asset URL with version parameter
     */
    public static function versionedAsset(string $path): string
    {
        $url = asset($path);
        $version = self::getFileVersion(public_path($path));

        return "{$url}?v={$version}";
    }

    /**
     * Get file version based on last modified time.
     *
     * @param string $filePath Full path to file
     * @return string Version string (timestamp or fallback)
     */
    public static function getFileVersion(string $filePath): string
    {
        return File::exists($filePath)
            ? (string) File::lastModified($filePath)
            : '1.0.0';
    }

    /**
     * Check if current URL contains trash parameter.
     *
     * @return string Trash parameter or empty string
     */
    public static function getTrashUrlParameter(): string
    {
        $currentUrl = request()->getRequestUri() ?? url()->current();

        return str_contains($currentUrl, 'trash') ? '/trash' : '';
    }
}
