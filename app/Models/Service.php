<?php


namespace App\Models;

use App\Traits\HasMediaTrait;
use App\Traits\StatusModelTrait;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\belongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Service extends Model implements TranslatableContract
{
    use Translatable, StatusModelTrait, HasMediaTrait;

    /**
     * Translated attributes.
     *
     * @var string[]
     */
    public $translatedAttributes = [
        'name',
        'slug',
        'new_slug',
        'description',
        'content',
        'alt_image',
        'meta_title',
        'canonical',
        'meta_description',
        'meta_keywords',
    ];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['status', 'category_id', 'title_header_option', 'language'];
    /**
     * Appends attributes.
     *
     * @var string[]
     */
    protected $appends = ['statusData', 'service_image', 'serviceName'];

    /**
     * Category relation.
     *
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class)->with('translations');
    }

    /**
     * Return service image.
     *
     * @return string $imagePath
     */
    public function getServiceImageAttribute(): string
    {
        $image = $this->getFirstMediaUrl('service_image');
//        dd($image);
        if ($image) {
            return $image;
        }

        return asset('frontend/images/services/service-03.jpg');
    }

    /**
     * FaqPage relation.
     *
     * @return BelongsToMany
     */
    public function faqPage(): belongsToMany
    {
        return $this->belongsToMany(FrequentlyQuestion::class, 'faq_pages', 'service_id', 'frequently_question_id');
    }

    /**
     * Get service name.
     *
     * @return void
     */
    public function getServiceNameAttribute()
    {
        return ($this->translate('en') != null) ?
            $this->translate('en')?->name :
            $this->translate('ar')?->name;
    }

    public function sections(): HasMany
    {
        return $this->hasMany(ServiceSection::class)->orderBy('sorting', 'asc');
    }
}
