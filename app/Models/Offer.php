<?php

namespace App\Models;


use App\Traits\HasMediaTrait;
use App\Traits\StatusModelTrait;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Offer extends Model implements TranslatableContract
{
    use Translatable, StatusModelTrait, HasMediaTrait;

    public $translatedAttributes = ['name', 'description'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['price', 'discount_price', 'slug', 'category_id', 'expire_date', 'is_special', 'status'];
    /**
     * Appends attributes.
     *
     * @var string[]
     */
    protected $appends = ['offer_image', 'statusData'];

    /**
     * Category relation.
     *
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class)->with('translations');
    }

    /**
     * Branches relation.
     *
     * @return BelongsToMany
     */
    public function branches(): BelongsToMany
    {
        return $this->belongsToMany(Branch::class, 'branch_offer', 'offer_id', 'branch_id');
    }

    /**
     * @return BelongsToMany
     */
    public function bookings(): BelongsToMany
    {
        return $this->belongsToMany(Booking::class, 'booking_offer');
    }

    /**
     * Return offer image.
     *
     * @return string $imagePath
     */
    public function getOfferImageAttribute(): string
    {
        if ($image = $this->getFirstMediaUrl('offer_image')) {
            return $image;
        }

        return asset('frontend/images/offers/offer-02.jpg');
    }
}
