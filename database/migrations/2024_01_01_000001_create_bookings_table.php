<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_mobile');
            $table->datetime('booking_date');
            $table->datetime('attendance_date')->nullable();
            $table->enum('status', ['pending', 'confirmed', 'completed', 'cancelled', 'no_show'])->default('pending');
            $table->enum('booking_type', ['offers', 'doctor', 'invoice'])->default('offers');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->foreignId('doctor_id')->nullable()->constrained('doctors')->onDelete('set null');
            $table->string('ref_id_joele')->unique();
            $table->string('ref_id_pay')->nullable();
            $table->boolean('payment_success')->default(false);
            $table->string('offer_source')->nullable(); // where did he get the offer
            $table->decimal('total_amount', 10, 2)->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for better performance
            $table->index(['status', 'booking_date']);
            $table->index(['booking_type', 'branch_id']);
            $table->index(['customer_email', 'customer_mobile']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
