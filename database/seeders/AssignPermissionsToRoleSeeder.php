<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AssignPermissionsToRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('role_has_permissions')->truncate();

        $dontShow = Permission::FIELD_PERMISSION_DONT_SHOW;
        $readOnly = Permission::FIELD_PERMISSION_READ_ONLY;

        $developer_permissions = Permission::where('name', 'not like', "%$dontShow%")
            ->where('name', 'not like', "%$readOnly%")
            ->get();

        $developer = Role::findByName('developer');
        $this->giveTheRolePermissions($developer, $developer_permissions);

        $superAdmin = Role::findByName('super-admin');
        $this->giveTheRolePermissions($superAdmin, $developer_permissions);

    }

    /**
     * @param $role
     * @param $permissions
     */
    private function giveTheRolePermissions($role, $permissions): void
    {
        if ($role) {
            $roleName = Str::upper(optional($role)->name);
            $permissions_array = $permissions->pluck('name')->toArray();
            $role_permissions_array = $role->permissions->pluck('name')->toArray();
            if (!count(array_diff($permissions_array, $role_permissions_array))) {
                $this->command->warn("Permissions Already up to date => {$roleName}.");
                return;
            }
            $role->syncPermissions($permissions);
            $this->command->info(count($permissions)." {$role->name} Permissions Added successfully.");
        } else {
            $this->command->warn("Role Not Found.");
        }
    }
}
