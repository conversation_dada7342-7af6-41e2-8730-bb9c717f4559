

//sidebar styles
[direction="rtl"] {
	text-align: right;
    .deznav {
        text-align: right;
        
        .metismenu {
			padding-right: 0;
			padding-left: 30px;
			ul{
			
				&:after{
					left:auto;
					right:25px;
				}
				a{
					&:before{
						left:auto;
						right: 25px;
					}
				}
			}
			li{
				& > a{ 
					
					i{
					    padding: 0 0 0 1.5rem;
					}
					svg{
						margin-left:5px;
						margin-right:0;
						@at-root [data-sidebar-style="compact"]#{&} {
							left: auto;
							margin-left: auto;
							margin-right: auto;
						}
						@at-root [data-sidebar-style="icon-hover"]#{&} {
							margin-left:0;
						}
					}
				}
				ul a{
					transition: all 0.5s;
					padding-right: 5rem;
					padding-left: 0.625rem;
				}
			}
            li.active {

                &>.has-arrow {
                    &:after {
                        transform: rotate(45deg) translateY(-50%);
                    }
                }

            }

            .has-arrow {
                &:after {
                    left: 1.5625rem;
                    right: auto;
					
					@at-root [data-layout="horizontal"]#{&} {
						left: 1.125rem;
					}
					@at-root [data-sidebar-style="modern"]#{&} {
						-webkit-transform: rotate(-45deg) translateY(-50%);
						transform: rotate(-45deg) translateY(-50%);
					}
                }
            }

        }
    }
	&[data-sidebar-style="full"]{
		.deznav {
			.metismenu {
				ul{
					a{
						transition: all 0.5s;
						&:before{
							left:auto;
							right: 25px;
						}
					}
				}
			}
		}
	}
	&[data-sidebar-style="full"][data-layout="horizontal"]{
		.deznav .metismenu li > ul li a{
			padding:8px 45px 8px 20px;
			&:hover{
				padding-left:1rem;
				padding-right:3.7rem;
			}
		}
	}
	&[data-sidebar-style="full"][data-layout="vertical"]{
		.deznav .metismenu > li .has-arrow:after {
			left: 1.5rem;
			right: auto;
		}
		.deznav .metismenu > li > a {
			border-radius: 1rem 0 0 4rem;
		}
	}
	&[data-sidebar-style="mini"]{
		.deznav .metismenu > li > a > i {
			padding: 0;
		}
		&[data-layout="vertical"]{
			.deznav .metismenu > li > ul a.has-arrow:after{
				left: 1.5625rem;
				right: auto;
			}
		}
	}
	&[data-sidebar-style="compact"] {
		.deznav{
			.metismenu li{
				& > a i {
					padding: 0;
				}
				ul a {
					padding-right: 0.625rem;
					padding-left: 0.625rem;
				}
			}
		}
	}
    &[data-sidebar-style="full"][data-layout="vertical"] {
        .menu-toggle {
            .deznav {
                .metismenu {
                    li {
                        &>ul {
    
                            li:hover {
                                ul {
                                    right: 11.8125rem;
                                    left: 0;
									
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

