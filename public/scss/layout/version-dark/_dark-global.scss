
[data-theme-version="dark"] {
    .dropdown-menu {
        background-color: $d-bg;
		box-shadow: 0px 0px 0px 1px rgba(255, 255, 255,0.1);
		
		.dropdown-item {
			&.selected,
			&.selected.active,
			&.active,
			&:hover, 
			&:focus {
				background-color: $dark-card;
				color: $body-color;
			}
		}
    }
	.sidebar-right{
		.dropdown-menu{
			background-color:$white;
			box-shadow:0px 0px 40px 0px rgba(82, 63, 105, 0.1);
			.dropdown-item {
				&.selected,
				&.selected.active,
				
				&:focus {
					background-color:var(--rgba-primary-1);
					color:var(--primary);
				}
				&:hover{
					background-color:#f8f9fa;
					color:#514e5f;
					
					
				}
			}
		}
		
	}
	.sidebar-right .bootstrap-select .btn{
		border-color:$border!important;	
	}
    .form-control {
        background-color: $dark-card!important;
        border-color: $d-border;
        color: $white;
    }
    
    .card {
        background-color: $dark-card;
		box-shadow:none;
    }
	.btn-outline-dark:hover{
		background-color: $d-bg;
		border-color: $d-bg;
	}
	.tdl-holder input[type=text],
	.tdl-holder li{
	    background-color: $dark-card;
		border-color:$d-border;
	}
	.tdl-holder input[type=text]{
		&::placeholder {
			color: $d-ctl;
		}
	}
    .tdl-holder label:hover {
		background-color: $d-border;
		color: $white;
	}
	.text-muted {
		color: $d-ctl !important;
	}
	.modal-content{
		background:$dark-card;
	}
	.modal-footer,
	.modal-header{
		border-color: $d-border;
	}
	.close{
	    font-weight: 400;
		color: $white;
		text-shadow: none;
		
		&:hover{
			color: $white;
		}
	}
	.table strong {
		color: #fff;
	}
	.text-dark {
		color: $d-ctl !important;
	}
	.star-rating .product-review,
	.new-arrival-content .item{
		color: $white;
	}
	.custom-dropdown .dropdown-menu{
		border-color: $d-border;
	}
	.widget-stat .media > span{
	    background: $d-border;
	    border-color: $d-border;
		color:$white;
	}
	.plus-minus-input .custom-btn{
		background: $d-bg;
	    border-color: $d-border;
	}
	.dropdown-divider,
	.size-filter ul li{
		border-color: $d-border;
	}
	.custom-select{
	    border-color: $d-border;
		color: $d-ctl;
		background:$d-bg;
	}
	.nav-tabs{
		border-color: $d-border;
	}
	.mail-list .list-group-item.active i {
		color: $white;
	}
	hr{
		border-color: $d-border;
	}
	
	.grid-col{
		background:$d-bg;
	}
	
	.noUi-target{
		border-color:$d-border;
		border-radius: 8px;
		box-shadow: none;
		
		.noUi-connects{
			background:$d-border;
		}
	}
	.noUi-marker-large,
	.noUi-marker{
		background:$d-border;
	}
	.input-group-text{
		background:$d-bg!important;
		color:$white;
	}
	.gradient-bx{
		.border {
			border: 1px solid #f7f7f7 !important;		
		}		
	}
	.note-editor.note-frame{
		border-color:$d-border;
		.btn{
			color:$white;
		}
		.note-editing-area .note-editable{
			color:$white;
		}
	}
	.widget-media .timeline .timeline-panel{
		border-color:$d-border; 
	}
	.notification_dropdown .dropdown-menu-right .all-notification{
		border-color:$d-border; 
	}
	#user-activity .nav-tabs .nav-link{
		border-color:$d-border; 
	}
	.list-group-item-action{
		color:$body-color;
	}
	.list-group-item-action:focus, 
	.list-group-item-action:hover, 
	.list-group-item-action:focus{
		background-color:$d-bg; 
		border-color:$d-bg; 
	}
	.list-group-item.active{
		color:$white;
		border-color:var(--primary);
		&:focus, 
		&:hover, 
		&:focus{
			background-color:var(--primary); 
			border-color:var(--primary); 
			color:$white;
		}
	}
	.swal2-popup{
		background:$dark-card;
	}
	
	.dashboard_bar{
		color:$white;	
	}
	.text-black{
		color:$white!important;	
	}
	.apexcharts-text tspan{
		fill:$white;	
	}
	.apexcharts-legend-text{
		color: white!important;	
	}
	.table tbody tr td{
		color:$white;	
	}
	.dataTables_info{
		color:$white;	
	}
	.dataTables_wrapper .dataTables_paginate .paginate_button.previous, .dataTables_wrapper .dataTables_paginate .paginate_button.next{
		color:$white!important;		
	}
	.dataTablesCard{
		background-color:$dark-card;
		border-color:$d-border;
		.even{
			background-color:$dark-card!important;	
			&:hover{
				td{
					color:$black;	
				}
			}
		}
	}
	table.dataTable.patient-list tbody tr:hover{
		background:$dark-card!important;	
	}
	#example5{
		.even{
			&:hover{
				td{
					color:$white;	
				}	
			}	
		}	
	}
	table.dataTable thead th{
		border-color:$d-border;	
	}
	.btn-link{
		svg{
			path{
				stroke:$white;	
			}	
		}	
	}
	.bgl-dark{
		background:$d-bg;	
	}
	.bg-white{
		background-color:$dark-card!important;	
	}
	.assigned-doctor2 .owl-next, .assigned-doctor2 .owl-prev{
		background:$d-bg!important;		
	}
	
	.abilities-chart .ct-chart .ct-label{
		fill:$white;	
	}
	.review-table{
		border-color:$d-border;	
		.review-bx{
			border-color:$d-border;		
		}
	}
	
	.apexcharts-gridlines-horizontal{
		line{
			stroke:$d-border
		}	
	}
	.assigned-doctor .owl-next, .assigned-doctor .owl-prev,
	.compose-content .dropzone{
		background:$d-bg!important;	
	}
	.hamburger .line{
		background:$white!important;	
	}
	.menu-toggle .deznav .metismenu li > ul{
		background:$dark-card!important;	
	}
	.staf-info span{
		color: #fff;
	}
	.legendLabel {
		color: $white;
	}
	#chartBar,
	#chartBar1,
	#chartBar2,
	#line-chart{
		line{
			stroke:$d-border;
		}
	}
	.form-check.custom-checkbox .form-check-input,
	.compose-content .dropzone{
		border-color:$d-border!important;
	}
	.btn-close{
		background:transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath fill='white' d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
	}
	.fc-theme-standard td, .fc-theme-standard th,
	.fc-theme-standard .fc-scrollgrid.fc-scrollgrid-liquid, .fc-scrollgrid, table,
	.fc .fc-button-group > .fc-button,
	.fc-daygrid-dot-event{
		border-color:$d-border;
	}
	.new-arrival-content h4 a, .new-arrival-content .h4 a,
	.new-arrival-content p,
	.table thead th{
		color:$white;
	}
	/* .form-check-input[type="radio"],
	.form-check-input[type="checkbox"]{
		border-color:$d-border;
	} */
	.form-check-input{
		border-color:$d-border;
		
		&:checked{
			border-color:var(--primary);
		}
	}
	
	.select2-container--open .select2-dropdown,
	.select2-container--default .select2-results__option--selected,
	.select2-results__options li{
		background-color:$d-bg;
	}
	.select2-container--default .select2-results__option--highlighted[aria-selected]{
		background-color:$dark-card;
	}
	.border-end{
		border-color:$d-border!important;
	}
	.form-control::-webkit-file-upload-button{
		background-color:$d-bg!important;	
		color:$white;
	}
	.form-control:hover{
		&::-webkit-file-upload-button{
			background-color:$d-bg!important;	
		}
	}
	.ck.ck-editor .ck-content{
		background-color:$d-bg!important;
	 }
	 .ck.ck-editor .ck.ck-toolbar{
		background-color:$d-bg;
	 }
	 .ck.ck-content.ck-editor__editable.ck-rounded-corners.ck-editor__editable_inline.ck-blurred{
		border-color:$d-border;
	 }
	 .ck.ck-toolbar .ck.ck-toolbar__separator{
		display:none!important;
	 }
	 .daterangepicker .calendar-table{
		border-color:transparent;
	 }
	 .daterangepicker .drp-buttons{
		border-color:$d-border;
	 }
	 .daterangepicker{
		background:$d-bg;
		border-color:$primary;
		.calendar-table{
			border-color:$primary;
			background:$d-bg;
			.table-condensed{
				td{
					&:hover{
						background-color:$primary;
						color:$white;
					}
				}
			}
		}
        &:after{
            border-bottom: 6px solid $d-bg;
        }
	}
    
    .daterangepicker select.hourselect
    , .daterangepicker select.minuteselect
    , .daterangepicker select.secondselect
    , .daterangepicker select.ampmselect{
            background: $d-bg;
            border: 1px solid $d-border;
            color:$white;
    }
    
	.daterangepicker td.off, 
	.daterangepicker td.off.in-range,
	.daterangepicker td.off.start-date, 
	.daterangepicker td.off.end-date{
		background-color:transparent;
		&:hover{
			background-color:$primary;
			color:$white;			
		}
	}
	.daterangepicker td.off,{
		color:$white;
	}
	button.cancelBtn.btn.btn-sm.btn-inverse{
		background-color:$dark-card;
	}
	.clockpicker-popover .popover-content{
		background-color:$dark-card;
	}
	.clockpicker-plate{
		background-color:$d-bg;
	}
	.clockpicker-popover .popover-title{
		background-color:$d-bg;
		color:$white;
	}
	.picker{
        color:#999;
    }
    .dtp > .dtp-content{
        background:$d-bg;
    }
    .dtp table.dtp-picker-days tr > td > a{
       color: #68686a; 
       &.selected{
           color:$white;
       }
    }
	 .btn-link g [fill] {
		fill:$white;
	 }
	 .form-head .search-area{
		
		.input-group-text{
			background-color:$dark-card!important;
		}
	 }
	 .element-menu-inner,
	 .dz-card .card-body.code-area{
		background-color: $dark-card;
	 }
	 .element-nav li{
		&:after{
			background-color:$white;
		}
		&.active{
			&:after{
				background-color:var(--primary);	
			}
		}
	 }
	 .card-body{
		&.tab-icon{
			.nav-link{
				&.active{
					color:var(--primary);
					border-color:$d-border;
				}
				&:hover{
					color:var(--primary);
					border-color:$d-border;
				}
			}
		}
	 }
	 table.dataTable{
		color:$body-color;
	 }
	 .svg-multi-color{
		path{
			fill:#fff;
		}
	 }
	 .hljs-tag .hljs-name{
		color:#bc391d;
	 }
	 .hljs-punctuation, 
	.hljs-tag {
		color: #7f8484;
	}
	.hljs-string{
		color:#257f8c;
	}
	.hljs-tag .hljs-attr{
		color:#7956af;
	}
	.dz-card .card-body.code-area .language-html{
		color:#cfcfc4;
	}
	.notification_dropdown .dropdown-menu-end .all-notification{
		border-color:$d-border;
	}
	.form-head .search-area .form-control{
		border-radius:3rem;
	}
	.form-head .search-area .input-group-append .input-group-text{
		border-radius:0  3rem 3rem 0;
	}
	.form-head .search-area{
		background-color:transparent;
	}

}
