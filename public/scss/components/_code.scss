pre {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    overflow: auto;
    font-size: 0.875em;
	-moz-tab-size: 16;
    tab-size: 2;
	code.hljs {
		display: block;
		overflow-x: auto;
		color: #fff;
		padding:0;
	}
} 

.hljs-tag .hljs-name{
    color: #bc4c00;
}
.hljs-tag .hljs-attr{
	color:#795da3;
}


.hljs-punctuation, 
.hljs-tag {
    color: #333;
}
.hljs-deletion, 
.hljs-number, 
.hljs-quote, 
.hljs-selector-class, 
.hljs-selector-id, 
.hljs-template-tag,
.hljs-type {
    color: #ffa8a8;
}
.hljs-string{
	color:#183691;
}