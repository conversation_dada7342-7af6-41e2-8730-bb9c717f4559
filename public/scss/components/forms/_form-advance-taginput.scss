

.tagsinput {
    overflow-y           : auto;
    display              : block;
    width                : 100%;
    height               : 3.4rem;
    padding              : .6rem 1.2rem;
    font-size            : 1.4rem;
    line-height          : 1.42857143;
    background-image     : none;
    -webkit-border-radius: .4rem;
    border-radius        : .4rem;

    span.tag {
        display              : block;
        float                : left;
        padding              : .3rem .8rem;
        text-decoration      : none;
        margin-right         : .5rem;
        margin-bottom        : .5rem;
        font-size            : 1.3rem;
        -webkit-border-radius: .3rem;
        border-radius        : .3rem;

        a {
            font-weight    : bold;
            text-decoration: none;
            font-size      : 1.1rem;
        }
    }

    input {
        width        : 8rem;
        margin       : 0;
        font-size    : 1.3rem;
        border       : 1px solid transparent;
        padding      : .5rem;
        background   : transparent;
        outline      : 0;
        margin-right : .5rem;
        margin-bottom: .5rem;
    }

    div {
        display: block;
        float  : left;
    }
}

.tags_clear {
    clear : both;
    width : 100%;
    height: 0;
}

.not_valid {
    background: #FBD8DB !important;
    color     : #90111A !important;
}