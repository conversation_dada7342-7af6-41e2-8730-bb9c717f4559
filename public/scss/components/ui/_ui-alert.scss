

.alert{
    border-radius: $radius;
	padding:1rem 1.5rem;
	p{
		line-height:1.5;
	}
}
.alert-square{
	border-radius:0;
}

.alert-rounded{
    border-radius: 30px;
}

// Basic Alert


.alert-primary{
    background: var(--rgba-primary-1);
    border-color: var(--rgba-primary-1);
    color:var(--primary);
	@at-root [data-theme-version="dark"] & {
		background: var(--rgba-primary-1);
		border-color: var(--rgba-primary-1);
	}
}

.alert-secondary{
    background: $secondary-light;
    border-color: $secondary-light;
    color: $secondary;
	@at-root [data-theme-version="dark"] & {
		background: $secondary-opacity;
		border-color: $secondary-opacity;
		color:$body-color;
	}
}


.alert-success{
    background: $success-light;
    border-color: $success-light;
    color: $success;
	@at-root [data-theme-version="dark"] & {
		background: $success-opacity;
		border-color: $success-opacity;
	}
}

.alert-warning{
    background: $warning-light;
    border-color: $warning-light;
    color: $warning;
	@at-root [data-theme-version="dark"] & {
		background: $warning-opacity;
		border-color: $warning-opacity;
	}
}

.alert-danger{
    background: $danger-light;
    border-color: $danger-light;
    color: $danger;
	@at-root [data-theme-version="dark"] & {
		background: $danger-opacity;
		border-color: $danger-opacity;
	}
}

.alert-info{
    background: $info-light;
    border-color: $info-light;
    color: $info;
	@at-root [data-theme-version="dark"] & {
		background: $info-opacity;
		border-color: $info-opacity;
	}
}

.alert-dark{
    background: $dark-light;
    border-color: $dark-light;
    color: $dark;
	@at-root [data-theme-version="dark"] & {
		background: $dark-opacity;
		border-color: $dark-opacity;
		color:$body-color;
	}
}

.alert-light{
    background: $light;
    border-color: $light;
    color: $dark;
	
}


// Alert alt

.alert-alt.alert-primary{
    border-left: 4px solid var(--primary);
}

.alert-alt.alert-secondary{
    border-left: 4px solid $secondary;
}


.alert-alt.alert-success{
    border-left: 4px solid $success
}

.alert-alt.alert-warning{
    border-left: 4px solid $warning;
}

.alert-alt.alert-danger{
    border-left: 4px solid $danger;
}

.alert-alt.alert-info{
    border-left: 4px solid $info;
}

.alert-alt.alert-dark{
    border-left: 4px solid $dark;
}
.alert-alt.alert-light{
    border-left: 4px solid darken($light,15%);
}



// Alert alt with solid color

.alert-alt.alert-primary.solid{
    border-left: 4px solid var(--rgba-primary-dark)!important;
}

.alert-alt.alert-secondary.solid{
    border-left: 4px solid darken($secondary,25%)!important;
}


.alert-alt.alert-success.solid{
    border-left: 4px solid darken($success, 25%)!important;
}

.alert-alt.alert-warning.solid{
    border-left: 4px solid darken($warning,25%)!important;
}

.alert-alt.alert-danger.solid{
    border-left: 4px solid darken($danger,25%)!important;
}

.alert-alt.alert-info.solid{
    border-left: 4px solid darken($info,25%)!important;
}

.alert-alt.alert-dark.solid{
    border-left: 4px solid darken($dark,25%)!important;
}
.alert-alt.alert-light.solid{
    border-left: 4px solid darken($light,25%)!important;
}


//Alert dismissable with solid color
.alert-dismissible.solid{
    .close{
        &:hover{
            color: $white;
            opacity: 1;
        }
    }
}






// Alert Solid 
.alert.alert-primary.solid{
    background:var(--primary);
    color: $white;
    border-color:var(--primary);
}

.alert.alert-secondary.solid{
    background: $secondary;
    color: $white;
    border-color: $secondary;
}
.alert.alert-success.solid{
    background: $success;
    color: $white;
    border-color: $success;
}

.alert.alert-warning.solid{
    background: $warning;
    color: $white;
    border-color: $warning;
}

.alert.alert-danger.solid{
    background: $danger;
    color: $white;
    border-color: $danger;
}

.alert.alert-info.solid{
    background: $info;
    color: $white;
    border-color: $info;
}


.alert.alert-dark.solid{
    background: $dark;
    color: $white;
    border-color: $dark;
}


.alert.alert-light.solid{
    background: $light;
    color: $dark;
    border-color: $light;
}




// Alert right icon

.alert-right-icon{
    &>span i{
        font-size: 18px;
        margin-right: 5px;;
    }
    .close{
        i{
            font-size: 16px;
        }
    }
}

// Alert Outline
.alert.alert-outline-primary{
    background: transparent;
    color:var(--primary);
    border-color:var(--primary);
}


.alert.alert-outline-secondary{
    background: transparent;
    color: $body-color;
    border-color: $secondary;
}


.alert.alert-outline-success{
    background: transparent;
    color: $success;
    border-color: $success;
}


.alert.alert-outline-info{
    background: transparent;
    color: $info;
    border-color: $info;
}


.alert.alert-outline-warning{
    background: transparent;
    color: $warning;
    border-color: $warning;
}

.alert.alert-outline-danger{
    background: transparent;
    color: $danger;
    border-color: $danger;
}

.alert.alert-outline-dark{
    background: transparent;
    color: $body-color;
    border-color: $dark;
}

.alert.alert-outline-light{
    background: transparent;
    color: $dark;
    border-color: $light;
}



// Alert Social
.alert-social{
    color: $white;
    .alert-social-icon{
        align-self: center;
        margin-right: 0.9375rem;
        i{
            font-size: 42px;
        }
    }
    &.facebook{
        background-color: $facebook;
    }
    &.twitter{
        background-color: $twitter;
    }
    &.linkedin{
        background-color: $linkedin;
    }
    &.google-plus{
        background-color: $google-plus;
    }
    .close{
        &:hover{
            opacity: 1!important;
            color: $white!important;
        }
    }
}


// Alert left icon

.left-icon-big{
    .alert-left-icon-big{
        align-self: center;
        margin-right: 0.9375rem;
        i{
            font-size: 35px;   
			line-height: 1;
        }
		
        // &.primary{
        //     background:var(--primary);
        //     color: $white;
        // }
    }
    .media-body{
        // margin-left: 100px;
    }
}
[direction="rtl"]{
	.left-icon-big .alert-left-icon-big,
	.alert-social .alert-social-icon{
		margin-left: 0;
		margin-right: 0.9375rem;
	}
}
.card.square-alerts .alert{
    border-radius: 0;    
}

.alert-dismissible .btn-close{
	i{
		display:none;
	}
}


