!function(e){var t,n,a,i,o,l,s,c,r,d,u,h,f,m,v,p,g,b,k,C,z,y,$,w,x,F,E=(t=e(window).width(),n=function(){e("#checkAll").on("change",function(){e("td input, .email-list .custom-checkbox input").prop("checked",e(this).prop("checked"))}),e(".checkAll").on("change",function(){e(this).closest(".check-data").find(".form-check-input").prop("checked",e(this).prop("checked"))})},a=function(){e(".nav-control").on("click",function(){e("#main-wrapper").toggleClass("menu-toggle"),e(".hamburger").toggleClass("is-active")})},i=function(){jQuery("#lightgallery").length>0&&lightGallery(document.getElementById("lightgallery"),{plugins:[lgThumbnail,lgZoom],selector:".lg-item",thumbnail:!0,exThumbImage:"data-src"})},o=function(){for(var t=window.location,n=e("ul#menu a").filter(function(){return this.href==t}).addClass("mm-active").parent().addClass("mm-active");n.is("li");)n=n.parent().addClass("mm-show").parent().addClass("mm-active")},l=function(){e("ul#menu>li").on("click",function(){let t=e("body").attr("data-sidebar-style");"mini"===t&&(console.log(e(this).find("ul")),e(this).find("ul").stop())})},s=function(){var t=window.outerHeight,t=window.outerHeight;(t>0?t:screen.height)&&e(".content-body").css("min-height",t+60+"px")},c=function(){e('a[data-action="collapse"]').on("click",function(t){t.preventDefault(),e(this).closest(".card").find('[data-action="collapse"] i').toggleClass("mdi-arrow-down mdi-arrow-up"),e(this).closest(".card").children(".card-body").collapse("toggle")}),e('a[data-action="expand"]').on("click",function(t){t.preventDefault(),e(this).closest(".card").find('[data-action="expand"] i').toggleClass("icon-size-actual icon-size-fullscreen"),e(this).closest(".card").toggleClass("card-fullscreen")})},r=function(){e('[data-action="close"]').on("click",function(){e(this).closest(".card").removeClass().slideUp("fast")}),e('[data-action="reload"]').on("click",function(){var t=e(this);t.parents(".card").addClass("card-load"),t.parents(".card").append('<div class="card-loader"><i class=" ti-reload rotate-refresh"></div>'),setTimeout(function(){t.parents(".card").children(".card-loader").remove(),t.parents(".card").removeClass("card-load")},2e3)})},d=function(){let t=e(".header").innerHeight();e(window).scroll(function(){"horizontal"===e("body").attr("data-layout")&&"static"===e("body").attr("data-header-position")&&"fixed"===e("body").attr("data-sidebar-position")&&(e(this.window).scrollTop()>=t?e(".deznav").addClass("fixed"):e(".deznav").removeClass("fixed"))})},u=function(){jQuery(".metismenu > .mm-active ").each(function(){!jQuery(this).children("ul").length>0&&jQuery(this).addClass("active-no-child")})},h=function(){jQuery(".bell-link").on("click",function(){jQuery(".chatbox").addClass("active")}),jQuery(".chatbox-close").on("click",function(){jQuery(".chatbox").removeClass("active")}),jQuery(".dz-chat-user-box .dz-chat-user").on("click",function(){jQuery(".dz-chat-user-box").addClass("d-none"),jQuery(".dz-chat-history-box").removeClass("d-none")}),jQuery(".dz-chat-history-back").on("click",function(){jQuery(".dz-chat-user-box").removeClass("d-none"),jQuery(".dz-chat-history-box").addClass("d-none")}),jQuery(".dz-fullscreen").on("click",function(){jQuery(".dz-fullscreen").toggleClass("active")})},f=function(){e(".btn-number").on("click",function(t){t.preventDefault(),fieldName=e(this).attr("data-field"),type=e(this).attr("data-type");var n=e("input[name='"+fieldName+"']"),a=parseInt(n.val());isNaN(a)?n.val(0):"minus"==type?n.val(a-1):"plus"==type&&n.val(a+1)})},m=function(){jQuery(".dz-demo-content").length>0&&new PerfectScrollbar(".dz-demo-content"),e(".dz-demo-trigger").on("click",function(){e(".dz-demo-panel").addClass("show")}),e(".dz-demo-close, .bg-close").on("click",function(){e(".dz-demo-panel").removeClass("show")}),e(".dz-demo-bx").on("click",function(){e(".dz-demo-bx").removeClass("demo-active"),e(this).addClass("demo-active")})},v=function(){jQuery(".dz-fullscreen").on("click",function(e){document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement?document.exitFullscreen?document.exitFullscreen():document.msExitFullscreen?document.msExitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen&&document.webkitExitFullscreen():document.documentElement.requestFullscreen?document.documentElement.requestFullscreen():document.documentElement.webkitRequestFullscreen?document.documentElement.webkitRequestFullscreen():document.documentElement.mozRequestFullScreen?document.documentElement.mozRequestFullScreen():document.documentElement.msRequestFullscreen&&document.documentElement.msRequestFullscreen()})},p=function(){var e={init:function(){var e=document.querySelectorAll(".draggable-zone");if(0===e.length)return!1;new Sortable.default(e,{draggable:".draggable",handle:".draggable.draggable-handle",mirror:{appendTo:"body",constrainDimensions:!0}}).on("drag:stop",()=>{setTimeout(function(){var e;e=0,jQuery(".dropzoneContainer").each(function(){e=jQuery(this).find(".draggable-handle").length,jQuery(this).find(".totalCount").html(e)})},200)})}};jQuery(document).ready(function(){e.init()})},g=function(){let t=e(".image-select");t.find("option").each((t,n)=>{let a=e(n),i=a.attr("data-thumbnail");i&&a.attr("data-content","<img src='%i'/> %s".replace(/%i/,i).replace(/%s/,a.text()))}),t.selectpicker()},b=function(){jQuery("#smartwizard").length>0&&e(document).ready(function(){e("#smartwizard").smartWizard()})},k=function(){e(".heart").on("click",function(){e(this).toggleClass("heart-blast")})},C=function(){jQuery("#ckeditor").length>0&&ClassicEditor.create(document.querySelector("#ckeditor"),{}).then(e=>{window.editor=e}).catch(e=>{console.error(e.stack)})},z=function(){jQuery(".show-pass").on("click",function(){jQuery(this).toggleClass("active"),"password"==jQuery("#dz-password").attr("type")?jQuery("#dz-password").attr("type","text"):"text"==jQuery("#dz-password").attr("type")&&jQuery("#dz-password").attr("type","password")})},y=function(){t>1024&&e(".metismenu  li").unbind().each(function(t){if(e("ul",this).length>0){var n=e("ul:first",this).css("display","block"),a=n.offset().left,i=n.width(),n=e("ul:first",this).removeAttr("style");e("body").height();var o=e("body").width();if(jQuery("html").hasClass("rtl"))var l=a+i<=o;else var l=a>0;l?e(this).find("ul:first").removeClass("left"):e(this).find("ul:first").addClass("left")}})},$=function(){if(jQuery(".dz-theme-mode").length>0){jQuery(".dz-theme-mode").on("click",function(){jQuery(this).toggleClass("active"),jQuery(this).hasClass("active")?(jQuery("body").attr("data-theme-version","dark"),setCookie("version","dark"),jQuery("#theme_version").val("dark")):(jQuery("body").attr("data-theme-version","light"),setCookie("version","light"),jQuery("#theme_version").val("light")),e(".default-select").selectpicker("refresh")});var t=("version");jQuery("body").attr("data-theme-version",t),jQuery(".dz-theme-mode").removeClass("active"),setTimeout(function(){"dark"===jQuery("body").attr("data-theme-version")&&jQuery(".dz-theme-mode").addClass("active")},1500)}},w=function(t){"use strict";parseInt(e(".header").css("height"),10),e(".navbar-nav .scroll").on("click",function(t){if(t.preventDefault(),jQuery(".navbar-nav .scroll").parent().removeClass("active"),jQuery(this).parent().addClass("active"),""!==this.hash){var n=parseInt(e(this.hash).offset().top,10),a=parseInt(e(".header").css("height"),10);e("html, body").animate({scrollTop:n-a},800,function(){})}}),x()},x=function(e){if(jQuery(".navbar-nav").length>0){var t=parseInt(jQuery(".header").height(),10);jQuery(document).on("scroll",function(){var e=jQuery(this).scrollTop();jQuery(".navbar-nav .scroll").each(function(){var n=jQuery(this);if(jQuery(n.attr("href")),void 0!=jQuery(this.hash).offset())var a=parseInt(jQuery(this.hash).offset().top,10);else var a=0;a-t<=e&&(n.parent().addClass("active"),n.parent().siblings().removeClass("active"))})})}},F=function(){jQuery(window).on("scroll",function(){if(jQuery(".sticky-header").length>0){var t=jQuery(".sticky-header");e(window).scrollTop()>t.offset().top?t.addClass("is-fixed"):t.removeClass("is-fixed")}})},{init:function(){n(),a(),o(),l(),s(),c(),r(),d(),i(),u(),h(),f(),v(),p(),m(),k(),b(),y(),C(),z(),g(),$(),w(),F()},load:function(){g()},resize:function(){}});jQuery(document).ready(function(){e("#menu").metisMenu(),E.init(),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map(function(e){return new bootstrap.Popover(e)})}),jQuery(window).on("load",function(){e("#preloader").fadeOut(500),e("#main-wrapper").addClass("show"),e("select").selectpicker(),E.load()}),jQuery(window).on("resize",function(){E.resize()})}(jQuery);
