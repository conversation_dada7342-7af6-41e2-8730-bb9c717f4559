//  initilize Toaster messages
toastr.options = {
    'closeButton': true,
    'debug': false,
    'newestOnTop': false,
    'progressBar': true,
    'positionClass': 'toast-top-right',
    'preventDuplicates': false,
    'onclick': null,
    'showDuration': '300',
    'hideDuration': '8000',
    'timeOut': '8000',
    'extendedTimeOut': '8000',
    'showEasing': 'swing',
    'hideEasing': 'linear',
    'showMethod': 'fadeIn',
    'hideMethod': 'fadeOut',
}

var _meta_model_name = $('meta[name="module-class-name"]').attr('content')
var _meta_model_id = $('meta[name="meta-record-id"]').attr('content')

var select_assign_label = '.select2-assign-label'

var _lang = document.documentElement.lang
var _dir = $('meta[name="app-lang-dir"]').attr('content')
var _form_lang = $('#form-lang-name').val()
var _modal_form_lang = $('#change_form_lang').val() ?? _form_lang
var _form_dir = isNull(_form_lang) ? _dir : ((_form_lang === 'ar') ? 'rtl' : 'ltr')
// var _trans = window.trans.locale

var dont_care_characters = [/  /g, '+', '{', '}', ',', '.', ';', '<', '>', '(', ')', '_', '$', '%', '^', '*', '!', '?', '~', '@', '-/', '#/', '&/', '|', '\\']

var timepicker_format = 'hh:mm A'
var datepicker_format = 'DD/MM/YYYY'
var datetimepicker_format = 'DD/MM/YYYY hh:mm A'

function isNull(_val) {
    if (_val !== undefined && _val !== null && _val !== '' && _val.toString().length > 0) {
        return false
    }
    return true
}

function isNotNull(_val) {
    return !isNull(_val)
}

function onlyUnique(value, index, self) {
    return self.indexOf(value) === index
}

/**
 * Action Functions (CRUD) delete, restore, force-delete, activate, clone
 * -----------------------------------------------------------------------
 */
// Delete Item Functions
function deleteItem(_url, id, data = null, redirect = null, callback = null, reloadTableNames = null, _title = '', _text = null, _type = null, _icon = null) {
    if (_text == null) {
        _text = _trans.are_you_sure_delete
    }
    if (_type == null) {
        _type = 'warning'
    }
    if (_icon == null) {
        _icon = 'warning'
    }
    Swal.fire({
        title: _title,
        text: _text,
        type: _type,
        icon: _icon,
        showCancelButton: true,
        confirmButtonText: _trans.confirm_delete,
        cancelButtonText: _trans.cancel,
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: _url + '/' + id, type: 'delete', dataType: 'json', headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                }, data: data, beforeSend: function () {
                    $('.loader').show()
                }, success: function (data) {
                    if (callback) {
                        callback()
                    }
                    reloadTable('', reloadTableNames)
                    showSwalMessage(data)
                    if (redirect != null) {
                        window.location.href = redirect
                    }
                    return true
                }, error: function (xhr, ajaxOptions, thrownError) {
                    showErrorSwal(xhr, ajaxOptions, thrownError)
                }, complete: function () {
                    $('.loader').hide()
                },
            })
        }
    })
}

// Restore Item Function
function restoreItem(_url, id, data = null, redirect = null, callback = null, reloadTableNames = null, _title = '', _text = null, _type = null, _icon = null) {
    if (_title === '') {
        _title = _trans.restore_record
    }
    if (_text == null) {
        _text = _trans.are_you_sure_restore
    }
    if (_type == null) {
        _type = 'warning'
    }
    if (_icon == null) {
        _icon = 'warning'
    }
    Swal.fire({
        title: _title,
        text: _text,
        type: _type,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: _trans.confirm_restore,
        cancelButtonText: _trans.cancel,
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: `${_url}/${id}/restore`, type: 'post', dataType: 'json', headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                }, data: data, beforeSend: function () {
                    $('.loader').show()
                }, success: function (data) {
                    if (callback) {
                        callback()
                    }
                    reloadTable('', reloadTableNames)
                    showSwalMessage(data)
                    if (redirect != null) {
                        window.location.href = redirect
                    }
                    return true
                }, error: function (xhr, ajaxOptions, thrownError) {
                    showErrorSwal(xhr, ajaxOptions, thrownError)
                }, complete: function () {
                    $('.loader').hide()
                },
            })
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            // Swal.fire(
            //   'Cancelled',
            //   'Your imaginary file is safe :)',
            //   'error'
            // )
        }
    })
}

// ForceDelete Item Function
function forceDeleteItem(_url, id, data = null, redirect = null, callback = null, reloadTableNames = null, _title = '', _text = null, _type = null, _icon = null) {
    if (_title === '') {
        _title = _trans.force_delete_record
    }
    if (_text == null) {
        _text = _trans.are_you_sure_force_delete
    }
    if (_type == null) {
        _type = 'warning'
    }
    if (_icon == null) {
        _icon = 'warning'
    }

    Swal.fire({
        title: _title,
        text: _text,
        type: _type,
        icon: _icon,
        showCancelButton: true, // confirmButtonColor: '#171a1d',
        confirmButtonColor: '#c69500',
        confirmButtonText: _trans.confirm_force_delete,
        cancelButtonText: _trans.cancel,
        closeOnConfirm: true,
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: `${_url}/${id}/force-delete`, type: 'post', dataType: 'json', headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                }, data: data, beforeSend: function () {
                    $('.loader').show()
                }, success: function (data) {
                    if (callback) {
                        callback()
                    }
                    reloadTable('', reloadTableNames)
                    // showSwalMessage("تم الحذف بشكل نهائي بنجاح !");
                    showSwalMessage(data)
                    if (redirect != null) {
                        window.location.href = redirect
                    }
                    return true
                }, error: function (xhr, ajaxOptions, thrownError) {
                    showErrorSwal(xhr, ajaxOptions, thrownError)
                }, complete: function () {
                    $('.loader').hide()
                },
            })
        }
        return false
    })
}

// mass delete items Function
function massDeleteItem(_url, data = null, redirect = null, callback = null, reloadTableNames = null, _title = '', _text = null, _type = null, _icon = null) {
    if (_text == null) {
        _text = _trans.are_you_sure_delete
    }
    if (_type == null) {
        _type = 'warning'
    }
    if (_icon == null) {
        _icon = 'warning'
    }
    Swal.fire({
        title: _title,
        text: _text,
        type: _type,
        icon: _icon,
        showCancelButton: true,
        confirmButtonText: _trans.confirm_delete,
        cancelButtonText: _trans.cancel,
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: _url + '/mass-delete', type: 'post', dataType: 'json', headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                }, data: data, beforeSend: function () {
                    $('.loader').show()
                }, success: function (data) {
                    if (callback) {
                        callback()
                    }
                    reloadTable('', reloadTableNames)
                    showSwalMessage(data)
                    if (redirect != null) {
                        window.location.href = redirect
                    }
                    return true
                }, error: function (xhr, ajaxOptions, thrownError) {
                    showErrorSwal(xhr, ajaxOptions, thrownError)
                }, complete: function () {
                    $('.loader').hide()
                },
            })
        }
    })
}

// mass restore items Function
function massRestoreItem(_url, data = null, redirect = null, callback = null, reloadTableNames = null, _title = '', _text = null, _type = null, _icon = null) {
    if (_text == null) {
        _text = _trans.are_you_sure_restore
    }
    if (_type == null) {
        _type = 'warning'
    }
    if (_icon == null) {
        _icon = 'warning'
    }
    Swal.fire({
        title: _title,
        text: _text,
        type: _type,
        icon: _icon,
        showCancelButton: true,
        confirmButtonText: _trans.confirm_restore,
        cancelButtonText: _trans.cancel,
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: _url + '/mass-restore', type: 'post', dataType: 'json', headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                }, data: data, beforeSend: function () {
                    $('.loader').show()
                }, success: function (data) {
                    if (callback) {
                        callback()
                    }
                    reloadTable('', reloadTableNames)
                    showSwalMessage(data)
                    if (redirect != null) {
                        window.location.href = redirect
                    }
                    return true
                }, error: function (xhr, ajaxOptions, thrownError) {
                    showErrorSwal(xhr, ajaxOptions, thrownError)
                }, complete: function () {
                    $('.loader').hide()
                },
            })
        }
    })
}

// mass force delete items Function
function massForceDeleteItem(_url, data = null, redirect = null, callback = null, reloadTableNames = null, _title = '', _text = null, _type = null, _icon = null) {
    if (_text == null) {
        _text = _trans.are_you_sure_force_delete
    }
    if (_type == null) {
        _type = 'warning'
    }
    if (_icon == null) {
        _icon = 'warning'
    }
    Swal.fire({
        title: _title,
        text: _text,
        type: _type,
        icon: _icon,
        showCancelButton: true,
        confirmButtonText: _trans.confirm_force_delete,
        cancelButtonText: _trans.cancel,
    }).then((result) => {
        if (result.value) {
            $.ajax({
                url: _url + '/mass-force-delete', type: 'post', dataType: 'json', headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                }, data: data, beforeSend: function () {
                    $('.loader').show()
                }, success: function (data) {
                    if (callback) {
                        callback()
                    }
                    reloadTable('', reloadTableNames)
                    showSwalMessage(data)
                    if (redirect != null) {
                        window.location.href = redirect
                    }
                    return true
                }, error: function (xhr, ajaxOptions, thrownError) {
                    showErrorSwal(xhr, ajaxOptions, thrownError)
                }, complete: function () {
                    $('.loader').hide()
                },
            })
        }
    })
}

//------------------------------------{</>}----------------------------------

/**
 * Show error success messages (Swal, Toastr)
 * -----------------------------------------------------------------------
 */
function showSwalMessage($message = _trans.updated_successfully, $title = null, $type = null, $timer = null, $position = 'top-right', $showConfirmButton = false) {
    if (!$title) {
        $title = ''
    }
    if (!$type) {
        $type = 'success'
    }
    if (!$timer) {
        $timer = 1000
    }

    Swal.fire({
        title: $title,
        text: $message,
        type: $type,
        icon: $type,
        position: $position,
        showConfirmButton: $showConfirmButton,
        timer: $timer,
    })
}

// show alert messages
function showAlertMessage(item, data, bg_color = 'danger', $icon = 'bx bx-info-circle') {
    if (data.length > 0) {
        var _html = `<div class="alert alert-${bg_color}" style="margin-bottom: 5px !important;"><i class="${$icon}"></i>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button> ${data}</div>`
        $(item).html(_html)
    }
}

// show alert error messages
function showAlertMessages(item, data, bg_color = 'danger', $icon = 'bx bx-info-circle') {
    if (data.length > 0) {
        var _html = ''
        for (var count = 0; count < data.length; count++) {
            _html += `<div class="alert alert-${bg_color}" style="margin-bottom: 5px !important;"><i class="${$icon}"></i>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button> ${data[count]}</div>`
        }
        $(item).html(_html)
    }
}

// show toaster messages
function show_toastr_message(message, status, message_key = null) {
    if (message === undefined || !message.length) {
        console.log(message_key + ': ' + message)
        return ''
    }
    switch (message_key) {
        case 'message':
        case 'exception':
        default:
            switch (status) {
                case 422:
                case 419:
                case 404:
                case 403:
                case 401:
                    toastr.warning(message)
                    break

                case 500:
                case 501:
                case 502:
                    toastr.error(message)
                    break

                case 301:
                case 302:
                    toastr.info(message)
                    break

                case 201:
                case 200:
                    toastr.success(message)
                    break
            }

            return true
    }
}

// show toaster error messages
function showToasterErrors(xhr, error, code) {
    var errors = null
    if (xhr !== undefined) {
        try {
            errors = JSON.parse(xhr.responseText)
        } catch (e) {
            // if (!Array.isArray(xhr.responseText)){
            //   errors = [xhr.responseText];
            // }
            // toastr.error("An Error happened")
            console.log(xhr.responseText)
            return 0
        }
    } else {
        toastr.error('FORMAT ERROR: show error messages !!')
        return 0
    }
    if (errors.error) {
        errors = errors.error
    }
    if (Array.isArray(errors)) {
        var error_values = Object.values(errors)
        var error_keys = Object.keys(errors)
        var toasterShowed = false
        error_values.forEach(function (error, index) {
            if (toasterShowed) {
                return 0
            }
            toasterShowed = show_toastr_message(error, xhr.status, error_keys[index])
        })
    } else {
        toasterShowed = show_toastr_message(errors, xhr.status)
    }

}

// show sweet alert error
function showErrorSwal(xhr, ajaxOptions, thrownError) {
    var _message = xhr.responseText

    try {
        if (JSON.parse(xhr.responseText).message !== undefined) {
            _message = JSON.parse(xhr.responseText).message
        }
    } catch (err) {

    }

    Swal.fire({
        text: _message, type: 'error', icon: 'error', title: thrownError, showConfirmButton: false, timer: 1500,
    })
}

function hide_fields_errors(_form_id = null) {
    if (_form_id == null) {
        $('.is-invalid').removeClass('is-invalid')
        $('.error.invalid-feedback').remove()
    } else {
        $(_form_id + ' .is-invalid').removeClass('is-invalid')
        $(_form_id + ' .error.invalid-feedback').remove()
    }

}

function show_column_errors(errors, _modal_id = null) {
    hide_fields_errors(_modal_id)
    if (errors.error) {
        errors = errors.error
    } else {
        if (!Array.isArray(errors)) {
            return 0
        }
    }

    var error_values = Object.values(errors)
    var error_keys = Object.keys(errors)
    for (var i = 0; i < error_values.length; i++) {
        var error_value_array = error_values[i]
        var _column_name = error_keys[i]

        _column_name = _column_name.replace(".", "_");

        var error_text = error_value_array
        if (Array.isArray(error_text)) {
            error_text = error_text.join(' | ')
        }
        // if select
        var _select_class_input = `select.class-input-${_column_name}`
        if (_modal_id !== null) {
            _select_class_input = `${_modal_id} ${_select_class_input}`
        }
        $(_select_class_input).closest('.form-group').addClass('has-error')
        $(_select_class_input).last().addClass('is-invalid')
        $(_select_class_input).last().closest('.form-group').find('.error.invalid-feedback').remove()
        $(_select_class_input).closest('.flex-nowrap').after(`<span class="error invalid-feedback">${error_text}</span>`)

        // if another things

        var _input_class_input = `input.class-input-${_column_name}, textarea.class-input-${_column_name}`
        if (_modal_id !== null) {
            _input_class_input = `${_modal_id} ${_input_class_input}`
        }
        $(_input_class_input).last().addClass('is-invalid')
        $(_input_class_input).last().closest('.form-group').find('.error.invalid-feedback').remove()
        if ($(_input_class_input).parent().find('.input-group-append').length) {
            $(_input_class_input).parent().find('.input-group-append').after(`<span class="error invalid-feedback">${error_text}</span>`)
        } else {
            $(_input_class_input).after(`<span class="error invalid-feedback">${error_text}</span>`)
        }
    }
}

//------------------------------------{</>}----------------------------------

// Set Page Title
function setDocumentTitle(title) {
    var docTitle = title + ' - ' + $('meta[name=app-name]').attr('content')
    var documentTitle = docTitle.charAt(0).toUpperCase() + docTitle.slice(1)
    document.title = documentTitle
    return documentTitle
}

/*
 * Form Functions
 **************************************************************************/

// submit from function
function submitForm(_url, _formId = null, _data = null, _modelName = null, event = null, callback = null, _table_id = null, _button_class = null, _badge_count_id = null) {
    if (event !== null) {
        event.preventDefault()
    }
    if (_formId == null) {
        _formId = '#my_form'
    }
    var _modal_id = '#MyModal'

    if (_button_class === null) {
        _button_class = '#action'
    }

    if (_modelName !== null) {
        _formId = `#${_modelName}-form`
        _modal_id = `#${_modelName}-modal`
        if (_table_id == null) {
            _table_id = `#${_modelName}-table`
        }
        _button_class = `.btn-submit-${_modelName}`
    }

    if (_table_id == null) {
        _table_id = '#_table'
    }

    if (_data == null) {
        _data = $(_formId).serialize()
    } else {
        _data = new FormData(_formId)
    }
    let id = $(`${_formId} input[name=id]`).val()
    let buttonValue = $(`${_formId} input[name=button_action]`).val()
    let method = 'POST'
    if (buttonValue === 'update') {
        method = 'PUT'
        _url = _url + '/' + id
    }

    $.ajax({
        url: _url, method: method, data: _data, dataType: 'json', success: function (data) {
            if (data.error !== undefined && data.error.length > 0) {
                // showAlertMessages($(_formId + ' .form_output'), data.error)
                show_column_errors(data.error, _modal_id)
                return false
            }
            if (callback) {
                callback()
            }

            if (_modal_id !== null) {
                $(_modal_id).modal('hide')
            }
            if (_badge_count_id !== null) {
                $(_table_id).DataTable().ajax.reload((response) => {
                    $(_badge_count_id).text(response.data.length)
                })
            } else {
                $(_table_id).DataTable().ajax.reload()
            }

            $(_formId + ' .form_output').html('')
            showSwalMessage(data.success)
            hiddenForm(null, _modal_id, _formId)
            $(_button_class).attr('disabled', false)
            return true
        }, error: function (xhr, ajaxOptions, thrownError) {
            var data = JSON.parse(xhr.responseText)
            show_column_errors(data, _modal_id)

            // if (data.error) {
            //   showAlertMessages($(_formId + ' .form_output'), data.error)
            //   $(_button_class).attr('disabled', false)
            //   return false
            // }
            // else {
            //   showAlertMessage($(_formId + ' .form_output'), data)
            // }

        }, complete: function () {
            $(_button_class).attr('disabled', false)
        },

    })
}

// hide form function
function hiddenForm(title = null, modalId = null, _formId = null) {
    if (modalId == null) {
        modalId = '#MyModal'
    }
    if (_formId == null) {
        modalId = '#my_form'
    }
    resetForm(title, modalId, _formId)
    $(modalId).modal('hide')
}

// open form function
function openForm(title = null, modalId = null, _formId = null) {
    if (modalId == null) {
        modalId = '#MyModal'
    }
    resetForm(title, modalId, _formId)
    $(modalId).modal('show')
}

// reset form function
function resetForm(title = null, modalId = null, _formId = null) {
    if (title == null) {
        title = showAsTitle('')
    }
    if (modalId == null) {
        modalId = '#MyModal'
    }
    if (_formId == null) {
        _formId = '#my_form'
    }
    clearForm(modalId, _formId)
    $(_formId + ' input[name=button_action]').val('insert')
    $(modalId + ' .modal-title').html('<i class="bx bx-plus"></i> ' + title)
}

// clear form function
function clearForm(modalId = null, _form_id = null) {
    if (modalId === null) {
        modalId = '#MyModal'
    }
    if (_form_id === null) {
        _form_id = '#my_form'
    }
    $(modalId).modal('hide')
    var elementExists = document.getElementById(_form_id.replace('#', ''))
    if (elementExists) {
        $(_form_id)[0].reset()
    }
    $(_form_id + ' .form_output').html('')
    $(_form_id + ' textarea').text('')
    $('select[data-default]').each(function () {
        if ($(this).attr('data-default') !== '') {
            $(this).val($(this).data('default')).change()
        } else {
            $(this).val('').change()
        }
    })
    $(_form_id + ' .has-error,.error').removeClass('has-error')
    $(_form_id + ' input[name=id]').val('')
    $(_form_id + ' input[name=button_action]').val('insert')
    $(_form_id + ' .btn-submit').attr('disabled', false)
    $(_form_id + ' #change_form_lang').val(_modal_form_lang).trigger('change')
    hide_fields_errors(_form_id)
}

function showAsTitle(sentence = null, character = ' ') {
    if (sentence) {
        return sentence.replace(/[/\\?%*:|"<>\-_]/g, character)
    } else {
        return sentence
    }
}

//------------------------------------{</>}----------------------------------

function searchableModule(select2_element_class, _module = null, _parent_class = null) {
    $(select2_element_class).each(function () {
        // select lookup module
        let module = _module;

        if (_module == null) {
            module = $(this).data('related-model');
        }

        let dropDownParent = _parent_class;

        if (_parent_class == null) {

            if ($(this).closest('.modal-content').length > 0) {
                dropDownParent = $(this).closest('.modal-content')
            } else {
                dropDownParent = $(this).parent()
            }
        } else {
            dropDownParent = $(_parent_class);
        }

        $(this).select2({
            placeholder: 'Search for ..',
            minimumInputLength: 2,
            dropdownAutoWidth: true,
            dropdownParent: dropDownParent,
            width: '100%',
            ajax: {
                url: '/ajax/searchable-lookup-module', method: 'POST', dataType: 'json', data: function (params) {
                    params.page = params.page || 1
                    // Query parameters will be ?search=[term]&page=[page]
                    return {
                        module: module,
                        conditions: $(this).data('lookup-conditions'),
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        query: params.term,
                        page: params.page,
                        delay: 350, // wait 250 milliseconds before triggering the request
                    }
                }, processResults: function (data) {
                    // Transforms the top-level key of the response object from 'items' to 'results'
                    return {
                        results: data,
                    }
                },
            },
        })
    })
}

//------------------------------------{</>}----------------------------------
function get_table_buttons(_searchable = false) {
    var _table_buttons = [];
    if (_searchable) {
        _table_buttons.push({
            text: '<i class="bx bx-search"></i>', className: "buttons-collection-btn collection-btn-search",
        })
    }
    _table_buttons.push({
        text: '<i class="bx bx-sync"></i>',
        className: "btn-xs buttons-collection-btn",
        action: function (e, dt, node, config) {
            dt.ajax.reload(null, false);
        }
    }, {
        extend: 'colvis', text: '<i class="bx bx-layer"></i>',
    }, {
        extend: 'pageLength',
    })
    return _table_buttons;
};

// get datatable (create and get data)
function GetDatatable(_url, _columns = null, _filter_data = null, _id = null, _order = null, _searchable = true, _another_data = null) {

    var dTable = ''
    if (typeof _filter_data == 'string') {
        if (_filter_data === undefined || !_filter_data.length) {
            _filter_data = null
        }
        _filter_data = JSON.parse(_filter_data)
    }
    if (_id == null) {
        _id = '#_table'
    }
    if (_order == null) {
        _order = [0, 'desc']
    }

    var _table_buttons = get_table_buttons(_searchable);

    dTable = $(_id).DataTable({
        'processing': true,
        'serverSide': true,
        // dom: 'Bfrtip',
        width: '100%',
        colReorder: true,
        stateSave: true,
        stateDuration: (60 * 60 * 24) * 365,
        searching: _searchable,
        // pagingType: 'simple',
        buttons: _table_buttons,
        lengthMenu: [[5, 10, 25, 50, 75, 100, 150], [5, 10, 25, 50, 75, 100, 150]],
        pageLength: 10,
        scrollY:true,
        // scrollY: function () {
        //     var scrollY = 'calc(100vh - 310px)'
        //     if ($(window).width() < 992) {
        //         scrollY = 'calc(100vh - 350px)'
        //     }
        //     return scrollY
        // },
        scrollX: true,
        ajax: {
            url: _url, method: 'POST', data: function (data) {
                data.f = _filter_data
                data._other_data = _another_data
            }, error: function (xhr, error, code) {
                showToasterErrors(xhr, error, code)
            },
        },
        columns: _columns,
        order: _order,
        drawCallback: function () {
            // $('[data-toggle="tooltip"]').tooltip()
            // $('a.grouped_elements').fancybox()
        },
    })

    if (dTable.page.info().page !== undefined && dTable.page.info().page > 0) {
        dTable.page('first').draw(false)
    }

    // -------------------------------------------------------------------------
    // restrict server side search on press enter key
    $('div.dataTables_filter input').unbind()

    $('div.dataTables_filter input').bind('keyup', function (e) {
        if (e.keyCode == 13) {
            dTable.search(this.value).draw()
        }
        return
    })
    // -------------------------------------------------------------------------

    return dTable
}

// Reload Data table when append an error
// reload datatable
function reloadTable(tableName = '', reloadTableNames = null) {
    if (reloadTableNames) {
        reloadTableNames.forEach(function (item) {
            $(item).DataTable().ajax.reload()
        })
    }
    $('#' + tableName + '_table').DataTable().ajax.reload()
}

function clickableTableRows(dTable) {
    // Select Row in DataTable
    $(document).on('click', '#_table tbody tr', function () {

        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected')
        } else {
            dTable.$('tr.selected').removeClass('selected')
            $(this).addClass('selected')
        }
    })

    // Visit Link On DblClick
    $(document).on('dblclick', '#_table tbody tr', function () {
        var data = dTable.row(this).data()
        if (data.show_link) {
            window.location.href = data.show_link
        }
    })

    // Visit Link On DblClick
    // $(document).on('click', '#_table tbody tr.selected', function () {
    //   var data = dTable.row(this).data()
    //   if (data.show_link) {
    //     window.location.href = data.show_link
    //   }
    // })
    // -------------------------------------------------------------------------

}

//------------------------------------{</>}----------------------------------

/**
 * Editable Functions
 * -----------------------------------------------------------------------
 */
function editable_multi_select_initialization() {

    $('.editable.multi_select').editable({
        source: function () {
            return $(this).data('source')
        }, placement: 'right', autotext: 'always', select2: {
            width: '200px', multiple: true, placeholder: 'Please Select', tokenSeparators: [' ', ','],
        }, display: function (value) {
            var data_source = $(this).data('source')
            if (value != null && value !== '') {
                var html_data = get_multi_select_value_text(data_source, value)
                $(this).html(html_data.join(' '))
            }
        }, success: function (response, newValue) {
            editable_events(response, newValue, this)
            console.log('success')
        },

    }).on('hidden', function (e, reason) {
        if (reason === 'cancel' || reason === 'onblur') {
            //some action
        }
    })

    $('.editable.multi_select').on('shown', function () {
        var data_source = $(this).data('source')
        var editable = $(this).data('editable')
        var value = editable.value
        if (value != null && value != '') {
            var html_data = get_multi_select_value_text(data_source, value)
            $(this).html(html_data.join(' '))
        }
    })

    $('.editable.lookup_multi').each(function () {
        $(this).editable({
            // toggle:'dblclick',
            emptyMessage: '<em>Select Multi for ..</em>',
            select2: {
                placeholder: 'Search for select ..',
                minimumInputLength: 2,
                dropdownAutoWidth: true,
                multiple: true,
                width: '200px',
                ajax: {
                    url: '/ajax/searchable-lookup-module', method: 'POST', dataType: 'json', data: function (params) {
                        params.page = params.page || 1
                        var query = {
                            module: $(this).data('related-model'),
                            conditions: $(this).data('lookup-conditions'),
                            _token: $('meta[name="csrf-token"]').attr('content'),
                            query: params.term,
                            page: params.page,
                            delay: 350, // wait 250 milliseconds before triggering the request
                        }
                        // Query parameters will be ?search=[term]&page=[page]
                        return query
                    }, processResults: function (data) {
                        // Transforms the top-level key of the response object from 'items' to 'results'
                        return {
                            results: data,
                        }
                    },
                },
            },
            theme: 'bootstrap',
            search: true,
            dropdownParent: $('.show-info-sections #' + $(this).attr('id')),
            tags: false,
            tpl: '<select></select>',
            width: '100%',
            type: 'select2',
            success: function (response, newValue) {
                editable_events(response, newValue, this)
            },
            display: function (value, sourceData) {
                $(this).text(sourceData)
            },
        })
    })
}

function editable_typeahead_initialization() {
    var typeahead = $('.typeaheadjs')
    typeahead.each(function () {
        $(this).typeahead('destroy')
    })
    typeahead.each(function () {
        var _name = $(this).data('editable-slug')
        var _src = $(this).data('source')
        $(this).editable({
            typeahead: {
                hint: true, highlight: true, minLength: 1, name: _name, source: substringMatcher(_src),
            },
        })
    })

}

function editable_select2_initialization() {

    $('.editable.select2').each(function () {
        $(this).editable({
            // toggle:'dblclick',
            emptyMessage: '<em>Please select...</em>',
            select2: {
                ajax: {
                    dataType: 'json', url: $(this).data('source'), data: function (term, page) {
                        return {query: term}
                    }, results: function (data, page) {
                        return {results: data}
                    }, processResults: function (data, page) {
                        return {
                            results: data,
                        }
                    },
                },
            }, // dropdownAutoWidth: true,
            theme: 'bootstrap',
            search: true, // dropdownParent:'.editable-inline',
            dropdownParent: $('.show-info-sections #' + $(this).attr('id')),
            tags: false,
            tpl: '<select></select>', // dropdownAutoWidth: true,
            // multiple: false,
            width: '100%',
            type: 'select2',
            success: function (response, newValue) {
                editable_events(response, newValue, this)
            },
            display: function (value, sourceData) {
                $(this).text(sourceData)
            },
        })
    })

    $('.editable.lookup').each(function () {
        $(this).editable({
            // toggle:'dblclick',
            emptyMessage: '<em>Search for ..</em>',
            select2: {
                placeholder: 'Search for ..', minimumInputLength: 2, dropdownAutoWidth: true, width: '100%', ajax: {
                    url: '/ajax/searchable-lookup-module', method: 'POST', dataType: 'json', data: function (params) {
                        params.page = params.page || 1
                        var query = {
                            module: $(this).data('related-model'),
                            conditions: $(this).data('lookup-conditions'),
                            _token: $('meta[name="csrf-token"]').attr('content'),
                            query: params.term,
                            page: params.page,
                            delay: 350, // wait 250 milliseconds before triggering the request
                        }
                        // Query parameters will be ?search=[term]&page=[page]
                        return query
                    }, processResults: function (data) {
                        // Transforms the top-level key of the response object from 'items' to 'results'
                        return {
                            results: data,
                        }
                    },
                },
            },
            theme: 'bootstrap',
            search: true,
            dropdownParent: $('.show-info-sections #' + $(this).attr('id')),
            tags: false,
            tpl: '<select></select>',
            width: '100%',
            type: 'select2',
            success: function (response, newValue) {
                editable_events(response, newValue, this)
            },
            display: function (value, sourceData) {
                $(this).text(sourceData)
            },
        })
    })
}

function editable_initialization() {
    $('.editable').editable({
        // toggle:'dblclick',
        emptyMessage: '<em>Please write something...</em>', success: function (response, newValue) {
            editable_events(response, newValue, this)
        }, error: function (response, newValue) {
            if (response.status === 500) {
                $('.editable-error-block').html('System Error.')
            } else {
                let error = $.parseHTML(response.responseText)
                $('.editable-error-block').html(error)
            }
        },
    })
}

function get_multi_select_value_text(data_source, value) {
    var val1 = [value].toString().split(',')
    var html_data = []
    $.each(val1, function (i) {
        $.each(data_source, function (i2, _value) {
            if (_value.id == val1[i]) {
                html_data[i] = '<span class="badge badge-light-primary">' + $(`<p>${_value.text}</p>`).text() + '</span>'
            }
        })
    })
    return html_data
}

function editable_events(response, new_value = null, _this = null) {
    var _m_module_name = $('meta[name="module-name"]').attr('content')

    // Update Last Activity
    render_last_activity_js_date()

    //
    // // GET LEAD TRANSACTIONS
    // if ($(_this).attr('id') === 'editable_owner_id') {
    //   getTransactionAjax()
    // }

    // if response has message and message equal to on of (has_event,reload_info or reload_page)
    if (response === 'has_event' || response === 'reload_info') {
        // $("[data-toggle='popover']").popover('destroy');
        // reload_section_info in related_tables.blade.oho file
        reload_section_info().then(() => {
            hideOrShowSections()
            editable_multi_select_initialization()
            editable_select2_initialization()
            editable_initialization()
            popover_refresh()
            $('[data-toggle="tooltip"]').tooltip()  // reactive tooltips
            get_trigger_columns_array(_m_module_name).then(function (data) {
                hideShowEditableFieldsByValue(data)
            })

            get_layout_rules_js(_m_module_name).then(function (data) {
                execute_layout_rule_options(data, _m_module_name, true)
            })

            get_module_load_after_select(_m_module_name).then(function (data) {
                execute_load_after_select(data, _m_module_name, true)
            })
        })
    }
    if (response === 'reload_page') {
        location.reload()
    }

    // var editable = $(this).data('editable')
    // var option = editable.input.$input.find('option[value="VALUE"]'.replace('VALUE', newValue))
    // var newText = option.text()
    // $(this).attr('data-text', newText)
    // $(this).attr('data-value', newValue)
}

//------------------------------------{</>}----------------------------------
function popover_refresh() {
    $('[data-toggle="popover"]').popover({
        trigger: 'manual', html: true, animation: false
    }).on('mouseenter', function () {
        var _this = this
        $(this).popover('show')
        $('.popover').on('mouseleave', function () {
            $(_this).popover('hide')
        })
    }).on('mouseleave', function () {
        var _this = this
        setTimeout(function () {
            if (!$('.popover:hover').length) {
                $(_this).popover('hide')
            }
        }, 300)
    })

    $('.popover').remove()
}

//------------------------------------{</>}----------------------------------
/**
 * AJAX Functions
 * -----------------------------------------------------------------------
 */
async function getCurrenciesAjax(_get_item_url = null) {
    if (_get_item_url == null) {
        _get_item_url = `/ajax/get-all-currencies`
    }
    var LS_KEY = 'ls-key' + _get_item_url
    window.all_currencies = getLocalStorageWithExpiry(LS_KEY)
    if (!window.all_currencies) {
        await $.ajax({
            url: _get_item_url, method: 'GET', dataType: 'json', success: function (data) {
                window.all_currencies = data
                setLocalStorageWithExpiry(LS_KEY, data, 3600) // for one hour
            }, error: function (data) {
                console.log(data)
            }, complete: function (data) {

            },
        })
    }

    return window.all_currencies
}

//------------------------------------{</>}----------------------------------
// module load after select
async function get_module_load_after_select(module_name, _trigger_column = null) {
    let _get_item_url = `/ajax/get-load-after-select-columns`
    var LS_KEY = `ls-key/${module_name}` + _get_item_url
    var data = getLocalStorageWithExpiry(LS_KEY)

    if (!data) {
        await $.ajax({
            url: _get_item_url, method: 'post', dataType: 'json', data: {
                module: module_name, trigger_column: _trigger_column,
            }, success: function (response) {
                data = response
                setLocalStorageWithExpiry(LS_KEY, data, 3600 * 24) // for one day
            }, error: function (data) {
                console.log(data)
            },
        })
    }
    return data
}

/**
 * Layout rules and validation functions
 * -----------------------------------------------------------------------
 */
// Trigger Columns
async function get_trigger_columns_array(module_name, _trigger_column = null) {
    let _get_item_url = `/ajax/get-trigger-columns`

    var LS_KEY = `ls-key/${module_name}` + _get_item_url
    var data = getLocalStorageWithExpiry(LS_KEY)

    if (!data) {
        await $.ajax({
            url: _get_item_url, method: 'post', dataType: 'json', data: {
                module: module_name, trigger_column: _trigger_column,
            }, success: function (response) {
                data = response
                setLocalStorageWithExpiry(LS_KEY, data, 3600 * 24) // for one day
            }, error: function (data) {
                console.log(data)
            },
        })
    }
    return data

}

// layout rules
async function get_layout_rules_js(module_name, _trigger_column = null) {
    let _get_item_url = `/ajax/get-layout-rules`
    var LS_KEY = `ls-key/${module_name}` + _get_item_url
    var data = getLocalStorageWithExpiry(LS_KEY)

    if (!data) {
        await $.ajax({
            url: _get_item_url, method: 'post', dataType: 'json', data: {
                module: module_name, trigger_column: _trigger_column,
            }, success: function (response) {
                data = response
                setLocalStorageWithExpiry(LS_KEY, data, 3600 * 24) // for one day
            }, error: function (data) {
                console.log(data)
            },
        })
    }
    return data
}

// calculate columns rules
async function get_calculate_columns_array(module_name, _trigger_column = null) {
    let _get_item_url = `/ajax/get-calculate-columns`
    var LS_KEY = `ls-key/${module_name}` + _get_item_url
    var data = getLocalStorageWithExpiry(LS_KEY)

    if (!data) {
        await $.ajax({
            url: _get_item_url, method: 'post', dataType: 'json', data: {
                module: module_name,
            }, success: function (response) {
                data = response
                setLocalStorageWithExpiry(LS_KEY, data, 3600 * 24) // for one day
            }, error: function (data) {
                console.log(data)
            },
        })
    }
    return data
}

// Aggregate Columns
async function get_aggregate_columns_array(module_name, _trigger_column = null) {
    let _get_item_url = `/ajax/get-aggregate-columns`
    var LS_KEY = `ls-key/${module_name}` + _get_item_url
    var data = getLocalStorageWithExpiry(LS_KEY)
    if (!data) {
        await $.ajax({
            url: _get_item_url, method: 'post', dataType: 'json', data: {
                module: module_name,
            }, success: function (response) {
                data = response
                setLocalStorageWithExpiry(LS_KEY, data, 3600 * 24) // for one day
            }, error: function (data) {
                /* console.log(data) */
            },
        })
    }
    return data
}

// hide or show sections
function hideOrShowSections(class_name = null) {
    var hasField = false
    if (class_name == null) {
        // call 'repeater' to hide or show after then hide or show 'section-group-border'
        hideOrShowSections('.repeater')
        class_name = '.section-group-border'
    }
    $(class_name).map(function () {
        hasField = false
        var section = $(this)
        var section_group_items = $(this).find('.form-group')
        if (section_group_items.length) {
            section_group_items.map(function () {
                if (!$(this).hasClass('d-none')) {
                    hasField = true
                }
            })
            if (hasField === true) {
                section.removeClass('d-none')
            } else {
                section.addClass('d-none')
            }
        }
    })
}

// hide or show related columns first time
function changeTriggerColumns(_trigger_columns, showTriggerColumn = false) {
    var _trigger_column_unique_names = _trigger_columns.map(({slug}) => slug)

    // to hide or show related columns first time
    _trigger_column_unique_names.forEach(function (column_slug, _index) {
        var _field = `[data-trigger-slug=${column_slug}]`

        var _val = $(_field).val()
        if (_val) {
            _val = _val.toString()
        }

        $(_field).val('')
        $(_field).val(_val)
        $(_field).change()

        $(_field).parents('.form-group').removeClass('d-none')
    })
}

function get_show_condition_exists(column, _val) {
    var valueIsNotEmpty = false
    var valueIsEmpty = false
    if (column.values.includes('valueIsNotEmpty')) {
        valueIsNotEmpty = true
    }
    if (column.values.includes('valueIsEmpty')) {
        valueIsEmpty = true
    }

    var show_condition_exists = false
    // value is not empty
    if (valueIsNotEmpty && _val.length) {
        show_condition_exists = true
    }
    // value is empty
    else if (valueIsEmpty && !_val.length) {
        show_condition_exists = true
    }

    return show_condition_exists
}

function hideRelatedTriggerFieldsByChangeTriggerColumns(_trigger_columns, showTriggerColumn = false) {
    if (!_trigger_columns) {
        return
    }
    _trigger_columns.forEach(function (column, _index) {
        var _field = `[data-trigger-slug=${column.slug}]`

        // On Change Column (hide or show) and (make empty or get old value) related columns
        $(document).on('change keyup', _field, function (event) {
            event.preventDefault()

            let _val = $(this).val()
            if (_val) {
                _val = _val.toString()
            }

            var related_fields = column.related_fields
            var required_fields = related_fields.required_if
            var visible_fields = related_fields.visible_if

            required_fields.forEach(function (_related_column, _index) {
                let _related = `[data-trigger-slug=${_related_column.slug}]`

                var show_condition_exists = get_show_condition_exists(_related_column, _val)

                var related_formGroup = $(_related).parents('.form-group')
                var _related_has_prepend = related_formGroup.find('.input-group-prepend')

                // if (related_formGroup.length && isNotNull(_val)) {
                if (!_related_column.values.includes(_val) && !show_condition_exists) {
                    _related_has_prepend.attr('class') ? _related_has_prepend.removeClass('required') : $(_related).removeClass('required')
                } else {
                    // SET RELATED FIELDS AS REQUIRED WHEN CHANGE STATUS WHICH USE THIS RELATED FIELDS
                    _related_has_prepend.attr('class') ? _related_has_prepend.addClass('required') : $(_related).addClass('required')
                }
            })

            visible_fields.forEach(function (_related_column, _index) {
                var show_condition_exists = get_show_condition_exists(_related_column, _val)

                let _related = `[data-trigger-slug=${_related_column.slug}]`
                var related_formGroup = $(_related).parents('.form-group')

                if (_related_column.values.includes(_val) || show_condition_exists) {
                    related_formGroup.removeClass('d-none')
                    related_formGroup.next('hr').removeClass('d-none') // show <hr> after group
                } else {
                    related_formGroup.addClass('d-none')
                    related_formGroup.next('hr').addClass('d-none') // hide <hr> after group
                    $(_related).removeClass('is-invalid')
                    $(_related).next('.error.invalid-feedback').empty()
                    if ($(_related).hasClass('required')) {
                        $(_related).val('')
                    }
                }

                // show field
                $(_field).parents('.form-group').removeClass('d-none')

                // HIDE OR SHOW SECTIONS IN CREATE,EDIT and SHOW PAGES if has fields or not
                hideOrShowSections()

            })

        })

    })

    changeTriggerColumns(_trigger_columns, showTriggerColumn)
}

function hideShowEditableFieldsByValue(_trigger_columns) {
    if (!_trigger_columns) {
        return 0
    }
    _trigger_columns.forEach(function (column, _index) {
        var _field = `[data-editable-slug=${column.slug}]`

        let _val = $(_field).data('value')
        if (_val) {
            _val = _val.toString()
        }

        var related_fields = column.related_fields
        var required_fields = related_fields.required_if
        var visible_fields = related_fields.visible_if

        required_fields.forEach(function (_related_column, _index) {
            let _related = `[data-editable-slug=${_related_column.slug}]`

            var related_formGroup = $(_related).parents('.form-group')
            var _related_has_prepend = related_formGroup.find('.input-group-prepend')

            if (!_related_column.values.includes(_val)) {
                _related_has_prepend.attr('class') ? _related_has_prepend.removeClass('required') : $(_related).removeClass('required')
            } else {
                // SET RELATED FIELDS AS REQUIRED WHEN CHANGE STATUS WHICH USE THIS RELATED FIELDS
                _related_has_prepend.attr('class') ? _related_has_prepend.addClass('required') : $(_related).addClass('required')
            }
        })

        visible_fields.forEach(function (_related_column, _index) {
            let _related = `[data-editable-slug=${_related_column.slug}]`

            var show_condition_exists = get_show_condition_exists(_related_column, _val)

            var related_formGroup = $(_related).parents('.form-group')
            if (_related_column.values.includes(_val) || show_condition_exists) {
                related_formGroup.removeClass('d-none')
                related_formGroup.next('hr').removeClass('d-none') // show <hr> after group
            } else {
                related_formGroup.addClass('d-none')
                related_formGroup.next('hr').addClass('d-none') // hide <hr> after group
            }

            // show field
            $(_field).parents('.form-group').removeClass('d-none')

            // HIDE OR SHOW SECTIONS IN CREATE,EDIT and SHOW PAGES if has fields or not
            hideOrShowSections()
        })

    })

}

//------------------------------------{</>}----------------------------------

/**
 * DateTime, datepicker time functions
 * -----------------------------------------------------------------------
 */
function setDateTimeAndTextFieldValues(data, _form_id) {
    var form_inputs = document.querySelectorAll(`${_form_id} input, ${_form_id} select, ${_form_id} textarea`)
    // var date_inputs = ['date', 'time', 'datetime', 'datetime-local']
    var date_classes = ['timepicker-input', 'datepicker-input', 'datetimepicker-input']
    // get modal form element inputs and textarea and another things
    form_inputs.forEach(function (element) {
        var _element_id = $(element).data('id')
        var _element_type = $(element).attr('type')
        var tagName = $(element).prop('tagName')
        // select[name='title']  ,input[name='title'] ,textarea[name='title']
        var setInput = $(`${_form_id} ${tagName}[name=${_element_id}]`)
        // foreach all exist form inputs and set values using returned element from
        // ajax request

        setInput.val(data[_element_id])
        // By input type
        // if (date_inputs.includes(_element_type)) {
        //   if (data[_element_id] !== '' && data[_element_id] !== null) {
        //     if (_element_type === 'datetime-local' || _element_type === 'date') {
        //       var dateJsonVal = new Date(data[_element_id]).toJSON()
        //       if (dateJsonVal) {
        //         if (_element_type === 'date') {
        //           setInput.val(dateJsonVal.slice(0, 10))
        //         }
        //         if (_element_type === 'datetime-local') {
        //           setInput.val(dateJsonVal.slice(0, 19))
        //         }
        //       }
        //     }
        //   }
        // }

// By input Class
        if (data[_element_id] !== '' && data[_element_id] !== null) {
            date_classes.forEach(function (date_class) {
                if (setInput.hasClass(date_class)) {
                    switch (date_class) {
                        case 'timepicker-input':
                            setInput.datetimepicker({format: timepicker_format}).data('DateTimePicker').date(new Date(data[_element_id]))
                            break
                        case 'datepicker-input':
                            setInput.datetimepicker({format: datepicker_format}).data('DateTimePicker').date(new Date(data[_element_id]))
                            break
                        case 'datetimepicker-input':
                            setInput.datetimepicker({format: datetimepicker_format}).data('DateTimePicker').date(new Date(data[_element_id]))
                            break

                    }
                }
            })
        }
        $(`${_form_id} select[name=${_element_id}]`).change()
    })
}

function createDatePicker() {
    var datepicker_icons = {
        time: 'bx bx-timer',
        date: 'bx bx-calendar',
        up: 'bx bx-chevron-up',
        down: 'bx bx-chevron-down',
        previous: 'bx bx-chevron-left',
        next: 'bx bx-chevron-right',
        today: 'bx bx-screenshot',
        clear: 'bx bx-trash',
        close: 'bx bx-x',
    }

    $('body').delegate('.timepicker-input', 'focusin', function () {
        $(this).datetimepicker({
            icons: datepicker_icons, format: timepicker_format, useCurrent: false,
        })
    })

    $('body').delegate('.datepicker-input', 'focusin', function () {
        $(this).datetimepicker({
            icons: datepicker_icons, format: datepicker_format, useCurrent: false, widgetPositioning: {
                horizontal: 'right', vertical: 'bottom',
            },
        })
    })

    $('body').delegate('.datetimepicker-input', 'focusin', function () {
        $(this).datetimepicker({
            icons: datepicker_icons, format: datetimepicker_format, useCurrent: false, // debug : true,
        })
    })

}

function destroyDatePicker() {
    var timepickers = $('.timepicker-input')
    var datepickers = $('.datepicker-input')
    var datetimepickers = $('.datetimepicker-input')
    if (timepickers.length) {
        timepickers.datetimepicker().data('DateTimePicker').destroy()
    }
    if (datepickers.length) {
        datepickers.datetimepicker().data('DateTimePicker').destroy()
    }

    if (datetimepickers.length) {
        datetimepickers.datetimepicker().data('DateTimePicker').destroy()
    }
}

//------------------------------------{</>}----------------------------------

function refreshSessionToken() {
    $.ajax({
        url: '/ajax/refresh-session-token', type: 'post', dataType: 'json', headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        }, success: function (result) {
            $('meta[name="csrf-token"]').attr('content', result.token) // refresh meta

            var token = $('meta[name="csrf-token"]').attr('content')
            if (token) {
                // return xhr.setRequestHeader('X-XSRF-TOKEN', token)
            }
        }, error: function (xhr, status, error) {
            console.log(xhr)
        },
    }).then(function (d) {
        $('meta[name="csrf-token"]').attr('content', d.token)
    })
}

//------------------------------------{</>}----------------------------------

//------------------------------------{</>}----------------------------------
// OPEN MODAL
//------------------------------------{</>}----------------------------------

// get modal form quick create fields by module name
async function get_quick_modal_content(_module, _row_module, _row_id, hiddenColumnsJson = null) {
    let _get_item_url = `/ajax/get-quick-modal-content`
    await $.ajax({
        url: _get_item_url, method: 'post', dataType: 'json', data: {
            module: _module, row_module: _row_module, row_id: _row_id, hidden_columns: hiddenColumnsJson,
        }, beforeSend: function () {
            $('.loader').show()
        }, success: function (res) {
            $('.quick-modal-body').html(res)
            searchableModule('.select2-lookup-module')
            $(select_assign_label).select2({
                width: '100%',
            })
            _module = _module.replaceAll('_', '-')
            $(`#${_module}-modal`).modal('show')
        }, error: function (data) {
        }, complete: function () {
            $('.loader').hide()
        },
    })
}

// get modal form fields by module name
async function get_modal_content(_view_name, _row_module, _row_id, _field_prefix = null) {
    let _get_item_url = `/ajax/get-modal-content`

    return $.ajax({
        url: _get_item_url, method: 'post', dataType: 'json', data: {
            view_name: _view_name, row_module: _row_module, row_id: _row_id, field_prefix: _field_prefix,
        }, beforeSend: function () {
            $('.loader').show()
        }, success: function (data) {
            searchableModule('.select2-lookup-module')
            $(select_assign_label).select2({
                width: '100%',
            })
        }, error: function (data) {
        }, complete: function () {
            $('.loader').hide()
        },
    })
}

// get modal row data from db by module name
async function get_module_item_data(_module, _row_id, _with = null) {
    let module_name = _module
    if (_with == null) {
        _with = []
    }
    let related_name = _with
    let _get_item_url = `/ajax/get-module-item`
    let _save_status = false

    await $.ajax({
        url: _get_item_url, method: 'post', dataType: 'json', data: {
            with: related_name, module: module_name, id: _row_id,
        }, success: function (data) {
            if (data[related_name].length > 0) {
                _save_status = true
            }
        }, error: function (data) {
        },
    })

    return _save_status
}

// open new quick related module modal in module show page
function open_quick_modal(_module, _row_module, _row_id, hiddenColumnsJson = null) {
    $('.quick-modal-body').html('')
    get_quick_modal_content(_module, _row_module, _row_id, hiddenColumnsJson).then(function (res) {
        get_trigger_columns_array(_module).then(function (data) {
            hideRelatedTriggerFieldsByChangeTriggerColumns(data, true)
            createDatePicker()
        })
    })
}

function clearLocalStorageByVersion() {
    var LS_KEY = 'clear-local-storage-version'
    var clear_version = $('meta[name="clear-local-storage-version"]').attr('content')
    var localStorage_clear_version = localStorage.getItem(LS_KEY)

    if (!localStorage_clear_version) {
        localStorage.clear()
        localStorage.setItem(LS_KEY, clear_version)
    }

    if (clear_version !== localStorage_clear_version) {
        localStorage.clear()
        localStorage.setItem(LS_KEY, clear_version)
    }

}

async function getUserThemeColor() {
    // get theme color from local storage
    var themeColor = localStorage.getItem('theme_color')
    var rgbThemeColor = localStorage.getItem('rgb_theme_color')

    if (themeColor && rgbThemeColor) {
        document.documentElement.style.setProperty('--main-color', themeColor)
        document.documentElement.style.setProperty('--primary', themeColor)
        document.documentElement.style.setProperty('--rgb-main-color', rgbThemeColor)
        return 1
    }

    // If unauthenticated user dont send ajax request
    if (!$('meta[name="authenticated"]').attr('content')) {
        return
    }
    var _get_user_theme_url = '/ajax/get-user-theme-color'
    var LS_KEY = 'ls-key' + _get_user_theme_url
    var data = getLocalStorageWithExpiry(LS_KEY)

    if (!data) {
        await $.ajax({
            url: _get_user_theme_url, type: 'post', dataType: 'json', headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            }, data: {}, beforeSend: function () {
                $('.loader').show()
            }, success: function (res) {
                data = res
                if (data && data.theme_color) {
                    setLocalStorageWithExpiry(LS_KEY, data, 3600 * 24) // for one day
                }
            }, error: function (xhr, ajaxOptions, thrownError) {
                // showErrorSwal(xhr, ajaxOptions, thrownError)
            }, complete: function () {
                $('.loader').hide()
            },
        })
    }

    return data
}

function initializeTypeHeadFields(class_name = null) {
    if (class_name == null) {
        class_name = '.typeahead'
    }
    var typeahead = $(class_name)
    typeahead.each(function () {
        $(this).typeahead('destroy')
    })
    typeahead.each(function () {
        var _name = $(this).data('module-name')
        var _src = $(this).data('src')
        $(this).typeahead({
            hint: true, highlight: true, minLength: 1,
        }, {
            name: _name, source: substringMatcher(_src),
        })
    })
}

function substringMatcher(strs) {
    return function findMatches(q, cb) {
        var matches, substringRegex

        // an array that will be populated with substring matches
        matches = []

        // regex used to determine if a string contains the substring `q`
        substrRegex = new RegExp(q, 'i')

        // iterate through the pool of strings and for any string that
        // contains the substring `q`, add it to the `matches` array
        $.each(strs, function (i, str) {
            if (substrRegex.test(str)) {
                matches.push(str)
            }
        })

        cb(matches)
    }
}

//------------------------------------{</>}----------------------------------

/**
 * Subform Functions
 * -----------------------------------------------------------------------
 */
function subform_clear(element) {
    element.find('input').val('')
    element.find('textarea').val('')
    element.find('radio').attr('checked', false)
    element.find('checkbox').attr('checked', false)

    var newSelectId = 'select' + Date.now()

    element.find('select').each(function (key, input) {
        $(input).attr('id', `${newSelectId}-${key}`)
    })

    element.find('select').removeAttr('data-select2-id')
    element.find('[data-select2-id]').removeAttr('data-select2-id')

    element.find('select').parents('td').removeAttr('data-select2-id')
    element.find('select').parents('div').removeAttr('data-select2-id')
    element.find('select').val('').change()

    element.find('.error.invalid-feedback').empty()
    element.find('.has-error').toggleClass('has-error', '')
    element.find('.is-invalid').toggleClass('is-invalid', '')

    element.find('ol.attachments_list').remove()
}

function subform_initial_select2(list = null) {
    if (list !== null) {
        list.find('.select2.select2-container.select2-container--default').remove()
        list.find('.select2-assign-label').select2({
            'width': '100%',
        })

        /* list.find('.select2-assign-label').each(function(key, input){
            $(input).select2({
                'width': '100%',
            })
        }) */

        searchableModule(list.find('.select2-lookup-module'))
    } else {
        $('.select2-assign-label').select2({
            'width': '100%',
        })

        /* $('.select2-assign-label').each(function(key, input){
            $(input).select2({
                'width': '100%',
            })
        }) */

        searchableModule('.select2-lookup-module')
    }
}

function subform_enable_disable_add_new_row() {
    $('.repeater-create').prop('disabled', true)

    var inputElements = $('[data-repeater-list]').find('tr:last').find('input,select,textarea')
    inputElements.each(function () {
        $(this).on('change keyup', function () {
            // var tagName = $(this).prop('tagName')
            var hasVal = $(this).val()
            inputElements.each(function () {
                if ($(this).val()) {
                    $('.repeater-create').prop('disabled', false)
                    return hasVal = $(this).val()
                }
            })
            if (hasVal) {
                $('.repeater-create').prop('disabled', false)
            } else {
                $('.repeater-create').prop('disabled', true)
            }
        })
    })

    inputElements.each(function () {
        var hasVal = $(this).val()
        if (hasVal) {
            $('.repeater-create').prop('disabled', false)
            return true
        }
    })

    return false
}

//------------------------------------{</>}----------------------------------

/**
 * Textarea Functions
 * -----------------------------------------------------------------------
 */
function textarea_auto_resize(element, defaultHeight = 2) {
    if (!element.length) {
        return
    }
    element.height(defaultHeight)
    var new_height = element.height() + element[0].scrollHeight - 50
    if (new_height <= 350) {
        element.height(new_height)
    } else {
        element.height(350)
    }
    element.css({transaction: '1s ease-in-out'})
}

function textarea_show_read_more(className) {
    var maxLength = 400
    $(className).each(function () {
        var myStr = $(this).text()
        if ($.trim(myStr).length > maxLength) {
            var newStr = myStr.substring(0, maxLength)
            var removedStr = myStr.substring(maxLength, $.trim(myStr).length)
            $(this).empty().html(newStr)
            $(this).append('<span class="text-read-more text-red">read more...</span>')
            $(this).append('<span class="more-text">' + removedStr + '</span>')
        }
    })

    $(document).on('click', '.text-read-more', function () {
        $(this).siblings('.more-text').show()
        $(this).parent(className).append('<span class="hide-text-more text-red">hide text...</span>')
        $(this).hide()
    })

    $(document).on('click', '.hide-text-more', function () {
        $(this).parent().find('.more-text').hide()
        $(this).parent().find('.text-read-more').show()
        $(this).remove()
    })
}

//------------------------------------{</>}----------------------------------

/**
 * Render Functions
 * -----------------------------------------------------------------------
 */

// Go To AjaxController and render layouts/partials/_cards/_info.blade.php for
// all related list items
async function reload_module_section_info(_module, _id) {
    await $.ajax({
        url: '/ajax/render/show-info-sections', method: 'post', dataType: 'json', data: {
            rowId: _id, module: _module,
        }, beforeSend: function () {
            $('.loader').show()
        }, success: function (data) {
            $('.show-info-sections').html(data)
        }, complete: function () {
            $('.loader').hide()
        },
    })
}

function timeSince(date) {

    var seconds = Math.floor((new Date() - date) / 1000);

    var interval = seconds / 31536000;

    if (interval > 1) {
        return Math.floor(interval) + " years";
    }
    interval = seconds / 2592000;
    if (interval > 1) {
        return Math.floor(interval) + " months";
    }
    interval = seconds / 86400;
    if (interval > 1) {
        return Math.floor(interval) + " days";
    }
    interval = seconds / 3600;
    if (interval > 1) {
        return Math.floor(interval) + " hours";
    }
    interval = seconds / 60;
    if (interval > 1) {
        return Math.floor(interval) + " minutes";
    }
    return Math.floor(seconds) + " seconds";
}

function render_last_activity_js_date(_module_name = null, _row_id = null) {
    var aDay = 24 * 60 * 60 * 1000;
    var aSecond = 1000;
    var dateTime = new Date();
    var time = timeSince(new Date(dateTime - aSecond));

    let dateY = new Intl.DateTimeFormat('tr-TR', {year: 'numeric'}).format(dateTime);
    let dateM = new Intl.DateTimeFormat('tr-TR', {month: '2-digit'}).format(dateTime);
    let dateD = new Intl.DateTimeFormat('tr-TR', {day: '2-digit'}).format(dateTime);
    let dateH = new Intl.DateTimeFormat('tr-TR', {hour: '2-digit'}).format(dateTime);
    let dateMi = new Intl.DateTimeFormat('tr-TR', {minute: '2-digit'}).format(dateTime);
    let dateS = new Intl.DateTimeFormat('tr-TR', {second: '2-digit'}).format(dateTime);


    dateTime = `${dateY}-${dateM}-${dateD} ${dateH}:${dateMi}:${dateS}`;

    var last_activity_html = `<li class="nav-item">
    <span data-toggle="tooltip" title="" data-original-title="${dateTime}"><b class="text-capitalize text-dark">last activity</b>: ${time} ago</span>
    </li>`;

    $('.last-activity-date-class').html(last_activity_html);
    $('[data-toggle="tooltip"]').tooltip()
}


function render_last_activity_date_partial(_module_name = null, _row_id = null) {
    // Update Last Activity
    if (!_module_name) {
        _module_name = $('meta[name="module-name"]').attr('content')
    }
    if (!_row_id) {
        _row_id = $('meta[name="model-id"]').attr('content')
    }

    let _get_item_url = '/ajax/render/last-activity-date'
    var _show_class_element = '.last-activity-date-class'
    $.ajax({
        url: _get_item_url, method: 'post', dataType: 'json', data: {
            module: _module_name, rowId: _row_id,
        }, success: function (data) {
            $(_show_class_element).html(data)
        }, error: function (xhr, ajaxOptions, thrownError) {
            var data = JSON.parse(xhr.responseText)
            if (data.error) {
                showAlertMessages($(_show_class_element), data.error)
                return false
            } else {
                showAlertMessage($(_show_class_element), data)
            }
        },
    })
}

//------------------------------------{</>}----------------------------------

/**
 * Local Storage Functions
 * -----------------------------------------------------------------------
 */
function setLocalStorageWithExpiry(key, value, ttl) {
    const now = new Date()
    // `item` is an object which contains the original value
    // as well as the time when it's supposed to expire
    const item = {
        value: value, expiry: now.getTime() + (ttl * 1000),
    }

    localStorage.setItem(key, JSON.stringify(item))
}

function getLocalStorageWithExpiry(key) {
    const itemStr = localStorage.getItem(key)

    // if the item doesn't exist, return null
    if (!itemStr) {
        return null
    }

    const item = JSON.parse(itemStr)
    const now = new Date()

    // compare the expiry time of the item with the current time
    if (now.getTime() > item.expiry) {
        // If the item is expired, delete the item from storage
        // and return null
        localStorage.removeItem(key)
        return null
    }
    return item.value
}

// CLEAR STORAGE FIRST TIME
function clearStorageOneTime(localStorageKey) {
    try {
        if (!localStorageKey) {
            localStorageKey = 'storage_cleared'
        }
        if (!localStorage.getItem(localStorageKey).length) {
            localStorage.clear()
            localStorage.setItem(localStorageKey, 'storage has been cleared successfully')
        }
    } catch (e) {
        localStorage.clear()
        localStorage.setItem(localStorageKey, 'storage has been cleared successfully')
    }
}

//------------------------------------{</>}----------------------------------

/**
 * Clear Functions
 * -----------------------------------------------------------------------
 */
function clear_select2_elements(_className) {
    if (!_className) {
        _className = '.select2'
    }
    var _select_items = $(_className).select2()
    _select_items.each(function (i, item) {
        $(item).select2('destroy')
        $(item).next('.select2.select2-container').remove()
    })
}

$(document).ready(function () {
    // Copy btn function
    // new ClipboardJS('.clipboard-btn');
    $(document).on('click', '.clipboard-btn', function () {
        $('.clipboard-btn').attr('data-original-title', 'Copy');
        $(this).attr('data-original-title', 'Copied!');
        $(this).tooltip('show');
    });

    // Clear local storage on change version
    clearLocalStorageByVersion()
    //------------------------------------{</>}----------------------------------

    // Set user theme color as theme color
    getUserThemeColor().then((data) => {
        if (data && data.theme_color) {
            document.documentElement.style.setProperty('--main-color', data.theme_color)
            document.documentElement.style.setProperty('--primary', data.theme_color)
        }
    })
    //------------------------------------{</>}----------------------------------

    //------------------------------------{</>}----------------------------------
    $.ajaxSetup({
        headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content'),
        },
    })

    // set border color on select2 error
    $('.error').parent().find('.select2.select2-container').css('border', '1px solid red')

    // initialize select2
    $(select_assign_label).change(function () {
        $(this).parent().find('.select2.select2-container').css('border', 'none')
        $(this).parent().find('.error').remove()
    })

    // initialize tooltip
    $('[data-toggle="tooltip"]').tooltip()

    // initialize datetime picker, datepicker and timepicker
    createDatePicker()
    //------------------------------------{</>}----------------------------------

    // resize textarea to based content length
    $('textarea').map(function () {
        if (!$(this).parent().hasClass('editable-input')) {
            textarea_auto_resize($(this))
        }
    })
    //
    // on change textarea or keyup (resize textarea to based content length)
    $(document).on('focus keyup change', 'textarea', function () {
        textarea_auto_resize($(this), 50)
    })
    // Open ADD MODULE to create new related element
    $(document).on('click', '.btn-create-new-item', function (e) {
        e.preventDefault()
        textarea_auto_resize($('textarea'))

    })

    // textarea_show_read_more('.editable.font-weight-bold.text');
    //------------------------------------{</>}----------------------------------

    // Get Currencies Ajax Data
    // getCurrenciesAjax()

    // submit create and edit forms
    $('.action-btn').click(function () {
        $('#my_form').submit()
    })

    //------------------------------------{</>}----------------------------------
    $(document).on('click', '.dt-columns-toggle-item', function () {
        var _module = $(this).data('module')
        $(`#${_module}-table_wrapper`).find('.dt-button.buttons-collection.buttons-colvis').click()
    })

    //------------------------------------{</>}----------------------------------
    $(document).on('click', '.dt-page-length-toggle-item', function () {
        var _module = $(this).data('module')
        $(`#${_module}-table_wrapper`).find('.dt-button.buttons-collection.buttons-page-length').click()
    })

// initialize type head fields
    initializeTypeHeadFields()
    //------------------------------------{</>}----------------------------------

})

$(document).on('change', function () {
    $('[data-toggle="tooltip"]').tooltip()
})

$(document).on('click', '.collection-btn-search', function () {
    $(this).parents('.dataTables_wrapper').find('.dataTables_filter').toggleClass('d-none');
})
